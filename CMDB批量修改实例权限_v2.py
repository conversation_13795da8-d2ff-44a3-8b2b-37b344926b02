#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []  # 初始化返回列表，用于存储符合条件的数据
    page = 1  # 页码，初始为第一页
    page_size = 200  # 每页返回的数据条数
    while True:
        # 设置分页参数
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:  # 如果响应正常
            ret_list += ret["data"]["list"]  # 将当前页的数据添加到返回列表
            if len(ret["data"]["list"]) < page_size: # 如果返回的数据小于每页条数，说明已到达最后一页
                break
            # 否则，继续请求下一页
            page += 1
        else:
            print(ret) # 如果响应错误，打印错误信息并退出循环
            break
    return ret_list  # 返回所有符合条件的实例数据

def update_instance_permissions(object_id, instance_ids, users, fields, method="append"):
    """
    批量修改CMDB实例权限
    
    参数:
    object_id - 模型ID
    instance_ids - 实例ID列表
    users - 用户/用户组列表
    fields - 权限字段列表，如 updateAuthorizers, deleteAuthorizers
    method - 权限修改方法，可选值: append(追加), overwrite(替换), pull(移除)
    
    返回:
    响应结果的JSON
    """
    # 构建API URL
    url = "http://10.7.231.40:8079/permission/{}/instances/_batch".format(object_id)
    
    # 构建请求体
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }
    
    # 发送PUT请求
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    
    # 返回响应结果
    return response

def generate_result():
    assetResponsiblePerson_params ={
    "fields": {
        "instanceId": True,
        "assetResponsiblePerson": True
    },
    "query": {
        "$and":[    
        {
            "assetResponsiblePerson":{   
            "$exists":True 
            }
        },
        {
            "assetResponsiblePerson":{  
             "$ne":"None"
            }
        }
    ]
    }
}
    assets = cmdb_instance_search("DAIWAI_TEST_0326", assetResponsiblePerson_params)
    print u"查询到的资产实例总数:", len(assets)
    return assets

def main():
    # 模型ID
    object_id = "DAIWAI_TEST_0326"  # 替换为您的实际模型ID
    
    # 权限字段列表
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    
    # 权限修改方法
    method = "append"  # 可选: append, overwrite, pull
    
    # 获取资产实例数据
    assets = generate_result()
    print json.dumps(assets, ensure_ascii=False, indent=2)
    
    # 循环处理每个实例，调用权限修改接口
    for asset in assets:
        instance_id = asset.get("instanceId")
        user = asset.get("assetResponsiblePerson")
        if instance_id and user:
            instance_ids = [instance_id]
            users = [user]
            print u"正在处理实例ID: {}, 负责人: {}".format(instance_id, user)
            response = update_instance_permissions(
                object_id=object_id,
                instance_ids=instance_ids,
                users=users,
                fields=fields,
                method=method
            )
            print u"状态码:", response.status_code
            print u"响应内容:"
            try:
                print json.dumps(response.json(), indent=2, ensure_ascii=False)
            except:
                print response.text

if __name__ == "__main__":
    main()
#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    """
    通用CMDB实例查询函数，根据传入的模型ID和查询参数返回实例列表
    """
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list

def update_instance_permissions(object_id, instance_ids, users, fields, method="append"):
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }
    print json.dumps(payload, ensure_ascii=False, indent=2)
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    return response

def main():
    object_id = "ROUTE@ONEMODEL"
    user_object_id = "USER"
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    method = "append"

    assetResponsiblePerson_params = {
        "fields": {
            "instanceId": True,
            "_ASSETS_ROUTE": True
        },
        "query": {
            "$and": [
                {"_ASSETS_ROUTE": {"$exists": True}}
            ]
        }
    }

    router_instances = cmdb_instance_search(object_id, assetResponsiblePerson_params)
    print u"查询到的路由器实例总数:", len(router_instances)
    print "-----------遍历列表-----------"
    count = 0
    for route in router_instances:
        instance_id = route.get("instanceId")
        _ASSETS_ROUTE = route.get("_ASSETS_ROUTE", [])
        for asset in _ASSETS_ROUTE:
            arp = asset.get("assetResponsiblePerson")
            if arp and arp != "None":
                print arp
                ret = cmdb_instance_search(user_object_id, params={"fields": {"name": True}, "query": {"nickname": {"$eq": arp}}})
                print json.dumps(ret, ensure_ascii=False, indent=2)
                for item in ret:
                    name = item.get("name")
                    print name
                    if instance_id and name:
                        response = update_instance_permissions(
                            object_id=object_id,
                            instance_ids=[instance_id],
                            users=[name],
                            fields=fields,
                            method=method
                        )
                        print u"状态码:", response.status_code
                        print "响应内容:"
                        try:
                            print json.dumps(response.json(), indent=2, ensure_ascii=False)
                        except:
                            print response.text
        count += 1
    print "要设置的白名单总实例数", count

if __name__ == "__main__":
    main()
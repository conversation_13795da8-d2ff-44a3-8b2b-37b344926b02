# #!/usr/local/easyops/python/bin/python
# # -*- coding: utf-8 -*-

# import sys
# import requests
# import json

# HOST = EASYOPS_CMDB_HOST.split(":")[0]

# headers = {
#     'user': 'easyops',
#     'org': '2024021401',
#     'host': 'cmdb_resource.easyops-only.com',
#     'content-type': 'application/json'
# }

# # 兼容 Python 2 处理默认编码
# reload(sys)
# sys.setdefaultencoding("utf-8")

# def cmdb_instance_search(object_id, params={}):
#     url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
#     ret_list = []
#     page = 1
#     page_size = 200
#     while True:
#         params["page"] = page
#         params["page_size"] = page_size
#         ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
#         if ret["code"] == 0:
#             ret_list += ret["data"]["list"]
#             if len(ret["data"]["list"]) < page_size:
#                 break
#             page += 1
#         else:
#             print(ret)
#             break
#     return ret_list

# def update_instance_permissions(object_id, instance_ids, users, fields, method="append"):
#     url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
#     payload = {
#         "method": method,
#         "list": users,
#         "fields": fields,
#         "ids": instance_ids
#     }
#     response = requests.put(url, headers=headers, data=json.dumps(payload))
#     return response

# def main():
#     object_id = "ROUTE@ONEMODEL"
#     fields = ["updateAuthorizers", "deleteAuthorizers"]
#     method = "append"
#     assetResponsiblePerson_params = {
#         "fields": {
#             "instanceId": True,
#             "_ASSETS_ROUTE": True
#         },
#         "query": {
#             "$and": [
#                 {"_ASSETS_ROUTE": {"$exists": True}}
#                 # {"assetResponsiblePerson": {"$ne": "None"}}
#             ]
#         }
#     }
#     router = cmdb_instance_search(object_id, assetResponsiblePerson_params)
#     print u"查询到的资产实例总数:", len(router)
#     # print json.dumps(router, ensure_ascii=False, indent=2)
#     print "-----------遍历列表-----------"
#     for route in router:
#         # print json.dumps(route, ensure_ascii=False, indent=2)
#         instance_id = route.get("instanceId")
#         _ASSETS_ROUTE = route["_ASSETS_ROUTE"]
#         # print json.dumps(instance_id, ensure_ascii=False, indent=2)
#         # print json.dumps(_ASSETS_ROUTE, ensure_ascii=False, indent=2)
#         for i in _ASSETS_ROUTE[0].items():
#             # print i
#             # print i[1]
#             if "assetResponsiblePerson" in i:
#                 print(i[1])
#             # print instance_id

#         # instance_id = route.get("instanceId")
#         # user = route["_ASSETS_ROUTE"]["assetResponsiblePerson"]
#         # user = route["_ASSETS_ROUTE"]
#         # print json.dumps(instance_id, ensure_ascii=False, indent=2)
#         # print json.dumps(user, ensure_ascii=False, indent=2)
#     # for route in router:
#     #     instance_id = route.get("instanceId")
#     #     user = route.get("assetResponsiblePerson")
#     #     if instance_id and user:
#     #         instance_ids = [instance_id]
#     #         users = [user]
#     #         print u"正在处理实例ID: {}, 负责人: {}".format(instance_id, user)
#     #         response = update_instance_permissions(
#     #             object_id=object_id,
#     #             instance_ids=instance_ids,
#     #             users=users,
#     #             fields=fields,
#     #             method=method
#     #         )
#     #         print u"状态码:", response.status_code
#     #         print u"响应内容:"
#     #         try:
#     #             print json.dumps(response.json(), indent=2, ensure_ascii=False)
#     #         except:
#     #             print response.text

# if __name__ == "__main__":
#     main()




#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list

def update_instance_permissions(object_id, instance_ids, users, fields, method="append"):
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }
    print json.dumps(payload, ensure_ascii=False, indent=2)
    # response = requests.put(url, headers=headers, data=json.dumps(payload))
    # return response

def main():
    object_id = "ROUTE@ONEMODEL"
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    method = "append"
    assetResponsiblePerson_params = {
        "fields": {
            "instanceId": True,
            "_ASSETS_ROUTE": True
        },
        "query": {
            "$and": [
                {"_ASSETS_ROUTE": {"$exists": True}}
            ]
        }
    }
    router_instances = cmdb_instance_search(object_id, assetResponsiblePerson_params)
    print u"查询到的路由器实例总数:", len(router_instances)
    print "-----------遍历列表-----------"
    for route in router_instances:
        instance_id = route.get("instanceId")
        _ASSETS_ROUTE = route.get("_ASSETS_ROUTE", [])
        users = []
        for asset in _ASSETS_ROUTE:
            arp = asset.get("assetResponsiblePerson")
            if arp and arp != "None":
                users.append(arp)
        print instance_id
        print json.dumps(users, ensure_ascii=False, indent=2)
        if instance_id and users:
            print u"正在处理实例ID: {}, 负责人列表: {}".format(instance_id, users)
            response = update_instance_permissions(
                object_id=object_id,
                instance_ids=[instance_id],
                users=users,
                fields=fields,
                method=method
            )
            print u"状态码:", response.status_code
            print u"响应内容:"
            try:
                print json.dumps(response.json(), indent=2, ensure_ascii=False)
            except:
                print response.text
        else:
            print u"实例ID或负责人列表为空，跳过处理，实例ID: {}, 负责人列表: {}".format(instance_id, users)

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
import urllib3
import json
import sys
import urllib3
import requests
import datetime
import hmac
import hashlib
import time
from uuid import uuid4


reload(sys)
sys.setdefaultencoding("utf-8")
HOST = "***********"
server_ip = "https://***********:4433"
path = "/api/v1/resource/queryAllResource"
api_key = "8557d1565cf741a5baaa5249f19551ee"
api_id = "1831790"


def cal_signature(key, url, query=None, body=None):
    # 从url中解析path路径
    path = urllib3.util.parse_url(url).path

    # 按规则拼接query参数的kv_str("k=v")
    params_list = []
    if query is not None:
        sorted_query = sorted(query.items(), key=lambda x: x[0])
        for k, v in sorted_query:
            params_list.append(k + "=" + v)

    # 准备body参数，严格json字符串
    if body is not None:
        params_list.append(json.dumps(body, ensure_ascii=False, separators=(',', ':')))

    if len(params_list):
        str_to_sign = path + "?" + "&".join(params_list)
    else:
        # 如果不存在参数，以路径作为被签名的内容
        str_to_sign = path

    # 签名
    sig = hmac.new(key=key, msg=str_to_sign, digestmod=hashlib.sha256)
    return sig.hexdigest()


def prepare_auth_headers(url, query=None, body=None):
    """
    准备Open API的请求鉴权头部：
    x-ca-key: API ID，从控制台获取
    x-ca-sign: 请求签名，用API密钥等拼接出的字符串对请求体内容计算出的签名
    x-ca-timestamp: 时间戳，长度为10的整数字符串
    x-ca-nonce: 随机数，数字字母加横线的字符串，长度2~128位
    """
    app_id = api_id
    app_secret = api_key
    # 获取十位时间戳
    timestamp = str(int(round(time.time())))
    # 数字字母加横线的字符串，长度2~128位，这里我们使用uuid/v4生成这样一个字符串
    nonce = str(uuid4())

    # 按照下面规则拼接签名密钥
    key = "appId=%s&appSecret=%s&timestamp=%s&nonce=%s" % (app_id, app_secret, timestamp, nonce)
    sign = cal_signature(key, url, query, body)

    return {
        "x-ca-key": app_id,
        "x-ca-timestamp": timestamp,
        "x-ca-nonce": nonce,
        "x-ca-sign": sign,
    }


def get(path, query=None, headers=None):
    """
    get请求接口，该接口封装了openAPI鉴权的请求头部
    """

    if headers is None:
        headers = dict()
    url = server_ip + path
    auth_headers = prepare_auth_headers(url, query)
    headers.update(auth_headers)

    return requests.get(url=url, headers=headers, params=query, verify=False)


def get_response():
    query = {
        "pageIndex": "1"
    }
    vpn_data = list()
    
    while True:
        response = get(path,query=query).json()
        if response:
            count = len(response["data"]["data"])
            query["pageIndex"] = int(query["pageIndex"]) + 1
            query["pageIndex"] = str(query["pageIndex"])
            vpn_data.extend(response["data"]["data"])
            # print(response["data"]["pageIndex"])

        if count < 20:
                break
        
    return vpn_data


# 日期时间类型转换
def convert_time(time_str):
    # 移除括号内的内容
    time_str = time_str.split('(')[0].strip()
    # 先移除 GMT+0800 这种时区信息（因为strptime难以直接处理）
    time_str_without_tz = time_str.rsplit(' ', 1)[0]
    # 尝试解析时间
    try:
        struct_time = time.strptime(time_str_without_tz, '%a %b %d %Y %H:%M:%S')
        dt = datetime.datetime(*struct_time[:6])
        return str(dt)
    except ValueError as e:
        print('解析错误:', e)
        return None


# 写入cmdb
def cmdb_import(datas):
    url = "http://{}/object/VPN_BUSINESS/instance/_import".format(HOST)
    headers = {"user":"easyops","org":"2024021401","host":"cmdb_resource.easyops-only.com","content-type":"application/json"}
    payload = {
        "keys":["id"],
        "datas":datas
        }
    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    print(response.text)


if __name__ == "__main__":
    vpn_data = get_response()
    print(len(vpn_data))
    for i in vpn_data:
        i["createdAt"] = convert_time(i["createdAt"])
        i["updatedAt"] = convert_time(i["updatedAt"])

    for i in range(0,len(vpn_data),50):
        datas = vpn_data[i:i+50]
        cmdb_import(datas)
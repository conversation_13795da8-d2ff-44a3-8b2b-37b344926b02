#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-
import sys
import json
import requests

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

# CMDB 服务器主机名
HOST = EASYOPS_CMDB_HOST.split(":")[0]

def create_announcement():
    """
    创建公告通知
    
    返回:
    响应对象和JSON格式的响应结果
    """
    url = "http://***********:8271/api/sys_setting/v1/announce"
    
    headers = { 
        'org': str(EASYOPS_ORG), 
        'user': 'easyops' 
    }
    
    notification_data = { 
        "category": "other",  # 公告类别 
        "data": "这是一个测试内容4",  # 公告正文内容
        "title": "测试05080957",  # 公告标题 
        "isPublish": True  # 是否发布公告 
    }
    
    # 发送请求
    response = requests.post(url=url, headers=headers, data=json.dumps(notification_data))
    
    # 返回响应对象和JSON格式的响应
    return response, response.json()

def get_announcement_detail(announce_id):
    """
    获取公告通知详情
    
    参数:
    announce_id - 公告ID
    
    返回:
    响应对象和JSON格式的响应结果
    """
    url = "http://***********:8271/api/sys_setting/v1/announce/detail/{0}".format(announce_id)
    
    print url
    
    headers = { 
        'org': str(EASYOPS_ORG), 
        'user': 'easyops' 
    }
    
    # 发送GET请求
    response = requests.get(url=url, headers=headers)
    
    # 返回响应对象和JSON格式的响应
    return response, response.json()

def delete_announcement(announce_ids):
    """
    删除公告通知
    
    参数:
    announce_ids - 公告ID，多个ID用分号分隔
    
    返回:
    响应对象和JSON格式的响应结果
    """
    url = "http://***********:8271/api/sys_setting/v1/announce/{0}".format(announce_ids)
    
    headers = { 
        'org': str(EASYOPS_ORG), 
        'user': 'easyops' 
    }
    
    # 发送DELETE请求
    response = requests.delete(url=url, headers=headers)
    
    # 返回响应对象和JSON格式的响应
    return response, response.json()

if __name__ == "__main__":
    # 调用创建公告函数
    # response, result = create_announcement()
    
    # # 打印响应状态码
    # print "响应状态码:", response.status_code
    
    # # 格式化输出JSON结果
    # print json.dumps(result, indent=2, ensure_ascii=False)
    
    # 获取公告详情示例
    # announce_id = "63495c1295361"  # 实例ID
    # detail_response, detail_result = get_announcement_detail(announce_id)
    
    # # 打印详情响应状态码
    # print "\n获取公告详情响应状态码:", detail_response.status_code
    
    # # 格式化输出详情JSON结果
    # print json.dumps(detail_result, indent=2, ensure_ascii=False)

    # 删除公告示例
    announce_ids = "63495c1295361;63495dbcdf43e"  # 多个ID用分号分隔
    delete_response, delete_result = delete_announcement(announce_ids)
    
    # 打印删除响应状态码
    print "\n删除公告响应状态码:", delete_response.status_code
    
    # 格式化输出删除JSON结果
    print json.dumps(delete_result, indent=2, ensure_ascii=False)
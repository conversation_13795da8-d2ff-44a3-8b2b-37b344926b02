#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

def update_instance_permissions(object_id, instance_ids, users, fields, method="append"):
    """
    批量修改CMDB实例权限
    
    参数:
    object_id - 模型ID
    instance_ids - 实例ID列表
    users - 用户/用户组列表
    fields - 权限字段列表，如 updateAuthorizers, deleteAuthorizers
    method - 权限修改方法，可选值: append(追加), overwrite(替换), pull(移除)
    
    返回:
    响应结果的JSON
    """
    # 构建API URL
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    
    # 设置请求头
    headers = {
        'user': 'easyops',
        'org': '2024021401',
        'host': 'cmdb_resource.easyops-only.com',
        'content-type': 'application/json'
    }
    
    # 构建请求体
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }
    
    # 发送PUT请求
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    
    # 返回响应结果
    return response

def main():
    # 模型ID
    object_id = "DAIWAI_TEST_0326"  # 替换为您的实际模型ID
    
    # 实例ID列表
    instance_ids = [
        "63482104f923b",
        "6348210dfed4e",
        "63482d240e52e",
        "63482d240f212"
    ]
    
    # 用户/用户组列表
    users = ["用户注册测试8"]
    
    # 权限字段列表
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    
    # 权限修改方法
    method = "append"  # 可选: append, overwrite, pull
    
    # 调用批量修改权限函数
    response = update_instance_permissions(
        object_id=object_id,
        instance_ids=instance_ids,
        users=users,
        fields=fields,
        method=method
    )
    
    # 打印响应结果
    print u"状态码:", response.status_code
    print u"响应内容:"
    try:
        # 尝试格式化输出JSON响应
        print json.dumps(response.json(), indent=2, ensure_ascii=False)
    except:
        # 如果不是有效的JSON，直接打印文本
        print response.text

if __name__ == "__main__":
    main()

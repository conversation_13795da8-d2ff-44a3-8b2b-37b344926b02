#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import requests
import json

def setup_cmdb_whitelist():
    """
    批量设置CMDB模型白名单
    为指定的模型设置更新和删除权限白名单
    """
    # API URL
    url = "http://***********:8079/batch/object"
    
    # 需要设置白名单的模型ID列表
    object_ids = [
        "HOST", 
        "ASSETS", 
        "LOAD_BALANCE", 
        "ROUTE@ONEMODEL", 
        "SWITCH@ONEMODEL", 
        "FIREWALL@ONEMODEL"
    ]
    
    # 授权组
    auth_group = "信息技术总部网络安全组"
    
    # 构建请求数据
    data_list = []
    for object_id in object_ids:
        data_item = {
            "objectId": object_id,
            "info": {
                "updateAuthorizers": [
                    auth_group
                ],
                "deleteAuthorizers": [
                    auth_group
                ]
            }
        }
        data_list.append(data_item)
    
    # 构建完整请求体
    payload = json.dumps({
        "data": data_list
    })
    
    # 设置请求头
    headers = {
        'user': 'easyops',
        'org': '2024021401',
        'host': 'cmdb_resource.easyops-only.com',
        'content-type': 'application/json'
    }
    
    # 发送请求
    response = requests.request("PUT", url, headers=headers, data=payload)
    
    # 打印响应结果
    print("状态码:", response.status_code)
    print("响应内容:")
    try:
        # 尝试格式化输出JSON响应
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    except:
        # 如果不是有效的JSON，直接打印文本
        print(response.text)

if __name__ == "__main__":
    setup_cmdb_whitelist()
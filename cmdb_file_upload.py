#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-
import sys
import json
import requests
import os

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

# CMDB 服务器主机名
HOST = EASYOPS_CMDB_HOST.split(":")[0]


def upload_file(filepath):
    """
    上传文件到CMDB附件存储
    
    参数:
    filepath - 要上传的文件路径
    
    返回:
    文件信息字典，包含name, url, size等
    """
    # 获取文件大小
    size = os.path.getsize(filepath)
    
    # 构建请求URL
    url = "http://{0}:8138/api/v1/objectStore/bucket/cmdb-bucket/object".format(HOST)
    
    # 设置请求头
    headers = {
        "user": "easyops", 
        "org": str(EASYOPS_ORG)
    }
    
    # 打开文件准备上传
    files = {"file": open(filepath, "rb")}
    
    # 发送PUT请求上传文件
    response = requests.put(url, headers=headers, data={}, files=files)
    
    print "上传响应状态码:", response.status_code
    print "上传响应内容:", response.text
    
    # 处理响应结果
    if response.status_code == 200:
        result = response.json()
        objectName = result["data"]["objectName"]
        objectUrl = "api/gateway/object_store.object_store.GetObject/api/v1/objectStore/bucket/cmdb-bucket/object/" + objectName
        name = os.path.basename(filepath)
        
        # 返回文件信息
        file_info = {
            "name": name,
            "url": objectUrl,
            "size": size
        }
        return file_info
    else:
        print "文件上传失败"
        return {}

if __name__ == "__main__":
    # 测试上传文件
    file_path = "/tmp/test05081127.txt"  # 替换为实际文件路径
    
    if os.path.exists(file_path):
        file_info = upload_file(file_path)
        
        if file_info:
            print "\n文件上传成功:"
            print json.dumps(file_info, indent=2, ensure_ascii=False)
    else:
        print "文件不存在，请检查路径"
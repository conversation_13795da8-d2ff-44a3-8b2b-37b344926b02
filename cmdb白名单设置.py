#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import requests
import json

url = "http://***********:8079/batch/object"

payload = json.dumps({
  "data": [
    {
      "objectId": "TEST_DEMO0324",
      "info": {
        "updateAuthorizers": [
          "chenzhigang",
          "gengrl"
        ]
      }
    }
  ]
})
headers = {
  'user': 'easyops',
  'org': '2024021401',
  'host': 'cmdb_resource.easyops-only.com',
  'content-type': 'application/json'
}

response = requests.request("PUT", url, headers=headers, data=payload)

print(response.text)
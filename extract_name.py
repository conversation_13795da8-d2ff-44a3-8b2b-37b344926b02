#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json

data = [
  {
    "instanceId": "63034c0f6b53d",
    "_object_id": "USER",
    "name": "000156"
  }
]

def extract_names(data_list):
    names = []
    for item in data_list:
        name = item.get("name")
        if name:
            names.append(name)
    return names

if __name__ == "__main__":
    names = extract_names(data)
    for name in names:
        print(name)

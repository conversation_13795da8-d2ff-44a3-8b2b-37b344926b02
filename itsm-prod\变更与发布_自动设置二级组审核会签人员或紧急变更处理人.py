#!/usr/local/easyops/python/bin/python
# --*--coding:utf8--*-- 
import requests
import json 

HOST = EASYOPS_CMDB_HOST.split(":")[0]
# 将orderInfo字符串转换为Python对象，以便后续处理
orderInfo = json.loads(orderInfo)

# 从 CMDB 中检索实例数据
def cmdb_instance_search(object,params={}):
    # 构建搜索实例的 URL
    result = []
    url = "http://{}/object/{}/instance/_search".format(HOST,object)
    headers = {
        'org': str(EASYOPS_ORG),  # 组织 ID
        'user': 'easyops',  # 用户名（用于认证）
        'Content-Type': 'application/json',  # 请求的内容类型为 JSON
        "host": "cmdb_resource.easyops-only.com"  # 目标主机地址
    }
    
    #params = {"fields":{"isntanceId":True},"query":{"ip":{"$eq":ip}}}
    ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
    
    if ret["code"] == 0:
        if ret["data"]["list"]:
            result =  ret["data"]["list"]
    else:
        print ret
    return result  # 返回所有符合条件的实例数据

def get_department():
    """
    userTaskId字段为节点ID;
    Activity_19qoovk为"创建表单"节点的节点ID
    1、遍历所有节点(stepList)，取出userTaskId为"Activity_19qoovk"的节点
    2、取出该节点的formData字段，formData为表单所有的字段和值
    3、将formData字段转换为python对象,该对象为一个列表
    4、遍历formData列表，取出key为'h5foanexz5'的值,该值为表单"变更与发布__初始申请单"中标题为"请求信息"
    的容器id，这样可以定位到该容器
    5、往changedepartment空列表中追加该容器的值的第一个元素中的department(申请部门)这个字段的值
    6、遍历该容器的值的第一个元素中的changedepartment(实施部门)这个字段的值
    7、取出x["name"]的值，将实施部门（changedepartment）中每个部门的名称添加到changedepartment列表中。
    name为用户组模型中的组名称字段
    8、使用set()函数去重后返回changedepartment列表
    """

    # 定义一个空列表，用于存储实施部门
    changedepartment = []
    for step in orderInfo["stepList"]:
        if step["userTaskId"] == "Activity_19qoovk":
            formData = json.loads(step["formData"])
            # print json.dumps(formData,indent=2,ensure_ascii=False)
            for i in formData:
                if i["key"] == 'h5foanexz5':
                    changedepartment.append(i["values"][0]["department"])
                    for x in i["values"][0]["changedepartment"]:
                        changedepartment.append(x["name"])
                        
                # if i["key"] == "h5fp2afb5l":
                #     emergency = i["values"][0]["emergency"]["value"]
                #     if emergency == "0":
                #         user_list = []
                #         emergency_handler = i["values"][0]["emergency_handler"]
                #         for user in emergency_handler:
                #             user_list.append(user.get("name"))
                            
                #         print("user_list: ", user_list)
                #         users_str = ",".join(user_list)
                #         PutStr("assigneeUser",users_str)
                        
                #         exit(0)
                        
                        
    return list(set(changedepartment))
    
if __name__ == '__main__':
    """
    1、调用get_department()函数，返回changedepartment列表
    2、调用cmdb_instance_search()函数，传入cmdb模型ID和查询参数
    3、返回字段是"_members.name"和"_members.teamleader"的列表
    _members是关系字段，name是用户模型的"用户名"字段，teamleader是用户模型的"团队负责人"字段
    查询条件是name在changedepartment列表中;USER_GROUP是用户组模型的模型ID
    4、定义一个空列表teamleaders，用于存储团队负责人
    5、如果group_ret不为空，则遍历group_ret列表
    6、遍历每个元素的"_members"列表
    7、如果"_members"列表中的"teamleader"字段的值为"是"，则将该元素的"name"字段的值添加到teamleaders列表中
    8、将teamleaders列表转换为字符串，使用逗号分隔
    9、调用PutStr函数，将字符串传入"assigneeList"字段
    assigneeList为输出定义中的输出参数ID，对应输出参数标题为"二级组审核会签人员"
    说明：PutStr为内置函数，工具的输出，为用于流程传递值；需要先在"输出定义"中的"输出列"中先声明对应变量
    """
    changedepartment = get_department()
    print json.dumps(changedepartment,indent=2,ensure_ascii=False)
    group_params = {"fields":{"_members.name":1,"_members.teamleader":1},"query":{"name":{"$in":changedepartment}}}
    group_ret = cmdb_instance_search("USER_GROUP",params=group_params)
    #print json.dumps(group_ret,indent=2,ensure_ascii=False)
    teamleaders = []
    if group_ret:
        for i in group_ret:
            for x in i["_members"]:
                if x.get("teamleader") == u"是":
                    teamleaders.append(x["name"]) 
    print teamleaders
    users_str = ",".join(teamleaders)
    PutStr("assigneeList",users_str)



"""
脚本的主要功能是：自动获取变更单中相关部门的负责人，并将这些负责人作为“二级组审核会签人员”输出到流程中。
"""
[{"status": "running", "otime": "", "formData": "", "ctime": "2025-05-23 10:10:07", "toolStatus": "", "isSubStep": false, "instanceId": "635c418daf8ce", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "", "assignees": {"assigneeGroupList": [], "role": "assignee", "assigneeList": []}, "action": "", "mtime": "", "operator": "", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动节点1", "consignors": [], "userTaskId": "Activity_05ku7kw"}, {"status": "done", "otime": "2025-05-23 09:05:49", "formData": "[{\"key\":\"h6noiw9boh\",\"values\":[{\"h6noiw9boi\":\"\",\"h6noiw9boj\":[]}]},{\"key\":\"h6noiw9bol\",\"values\":[{\"user\":[{\"_impl_from\":[],\"_object_id\":\"USER\",\"_object_version\":24,\"_pre_ts\":**********,\"_ts\":**********,\"_version\":9,\"creator\":\"chenzhigang\",\"ctime\":\"2025-04-23 15:21:59\",\"deleteAuthorizers\":[],\"easyops_account_type\":\"normal\",\"instanceId\":\"6336cf4eefcee\",\"modifier\":\"miaofeng\",\"mtime\":\"2025-05-22 14:32:18\",\"name\":\"miao<PERSON>\",\"nickname\":\"苗峰\",\"org\":**********,\"readAuthorizers\":[],\"state\":\"valid\",\"team\":\"测试\",\"teamleader\":\"否\",\"updateAuthorizers\":[],\"user_email\":\"<EMAIL>\",\"user_tel\":\"***********\"}],\"department\":[],\"phone\":\"\",\"email\":\"\"}]},{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[],\"u_position\":\"\",\"itsc_uuid_\":\"h7lmt6gny0\"}]}]", "ctime": "2025-05-23 09:05:48", "toolStatus": "", "isSubStep": false, "instanceId": "635c332e173b4", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "2025-05-23 09:05:49", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "申请人", "consignors": [], "userTaskId": "Activity_0xw1mea"}, {"status": "done", "otime": "2025-05-23 09:06:08", "formData": "[{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[],\"u_position\":\"\",\"itsc_uuid_\":\"h7lmt6gny0\"}]}]", "ctime": "2025-05-23 09:05:49", "toolStatus": "", "isSubStep": false, "instanceId": "635c332ecccc5", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:06:08", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "网络IP设置", "consignors": [], "userTaskId": "Activity_1l1at5c"}, {"status": "done", "otime": "2025-05-23 09:06:45", "formData": "[{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[{\"_impl_from\":[],\"_object_id\":\"_IDCRACK\",\"_object_version\":15,\"_pre_ts\":1744706104,\"_ts\":1747720989,\"_version\":3,\"code\":\"F8\",\"creator\":\"easyops\",\"ctime\":\"2025-04-14 10:00:46\",\"deleteAuthorizers\":[],\"instanceId\":\"632b36ba18195\",\"layout\":[{\"instanceId\":\"632ccebc402ff\",\"occupiedU\":2,\"startU\":36,\"type\":\"STORAGE_DEVICE@ONEMODEL\"},{\"instanceId\":\"632ccad865a33\",\"occupiedU\":1,\"startU\":39,\"type\":\"SWITCH@ONEMODEL\"},{\"instanceId\":\"6323ea42dbe1e\",\"occupiedU\":2,\"startU\":3,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea430faa3\",\"occupiedU\":2,\"startU\":33,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea430fe27\",\"occupiedU\":2,\"startU\":30,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4346735\",\"occupiedU\":2,\"startU\":24,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4348805\",\"occupiedU\":2,\"startU\":10,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4349b8d\",\"occupiedU\":2,\"startU\":16,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea434d5c1\",\"occupiedU\":2,\"startU\":21,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea434e2a5\",\"occupiedU\":2,\"startU\":7,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4373b6e\",\"occupiedU\":2,\"startU\":27,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea43764d6\",\"occupiedU\":2,\"startU\":18,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea437815a\",\"occupiedU\":2,\"startU\":13,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"}],\"location\":\"北环数据中心生产区F8机柜\",\"modifier\":\"chenzhigang\",\"mtime\":\"2025-05-20 14:03:09\",\"name\":\"BHSC1_F8\",\"org\":**********,\"readAuthorizers\":[],\"status\":\"启用\",\"type\":\"4-POST-封闭式机柜\",\"unum\":42,\"updateAuthorizers\":[]}],\"u_position\":\"流程测试\",\"itsc_uuid_\":\"h7lmt6gny0\",\"asset_manage_group\":[]}]}]", "ctime": "2025-05-23 09:06:09", "toolStatus": "", "isSubStep": false, "instanceId": "635c334182789", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:06:45", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "机柜位置设置", "consignors": [], "userTaskId": "Activity_1npatnx"}, {"status": "done", "otime": "2025-05-23 09:06:57", "formData": "[]", "ctime": "2025-05-23 09:06:45", "toolStatus": "", "isSubStep": false, "instanceId": "635c336462d75", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:06:57", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动流转节点", "consignors": [], "userTaskId": "Activity_1becw28"}, {"status": "done", "otime": "2025-05-23 09:54:05", "formData": "[]", "ctime": "2025-05-23 09:06:57", "toolStatus": "", "isSubStep": false, "instanceId": "635c336f87e4a", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:54:05", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动节点1", "consignors": [], "userTaskId": "Activity_05ku7kw"}, {"status": "done", "otime": "2025-05-23 09:54:49", "formData": "[]", "ctime": "2025-05-23 09:54:05", "toolStatus": "", "isSubStep": false, "instanceId": "635c3df8ad4d4", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:54:49", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "二级组审核", "consignors": [], "userTaskId": "Activity_0a25wz8"}, {"status": "done", "otime": "2025-05-23 09:55:06", "formData": "[]", "ctime": "2025-05-23 09:54:50", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e2333ade", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:55:06", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "网络组审核", "consignors": [], "userTaskId": "Activity_1q8to7t"}, {"status": "done", "otime": "2025-05-23 09:55:21", "formData": "[]", "ctime": "2025-05-23 09:55:06", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e32962e2", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:55:21", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "领导审核", "consignors": [], "userTaskId": "Activity_0nuu42l"}, {"status": "done", "otime": "2025-05-23 09:55:41", "formData": "[]", "ctime": "2025-05-23 09:55:21", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e410ba50", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:55:41", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "网络组审核", "consignors": [], "userTaskId": "Activity_1q8to7t"}, {"status": "done", "otime": "2025-05-23 09:55:57", "formData": "[]", "ctime": "2025-05-23 09:55:41", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e53fe733", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:55:57", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "领导审核", "consignors": [], "userTaskId": "Activity_0nuu42l"}, {"status": "done", "otime": "2025-05-23 09:56:08", "formData": "[]", "ctime": "2025-05-23 09:55:57", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e63a3552", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:56:08", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "网络组审核", "consignors": [], "userTaskId": "Activity_1q8to7t"}, {"status": "done", "otime": "2025-05-23 09:56:10", "formData": "[]", "ctime": "2025-05-23 09:56:08", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e6e5d3b5", "subProcessInstanceStepId": "", "memo": "自动通过!", "isExtraAssignee": false, "etime": "2025-05-23 09:56:10", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动流转节点2", "consignors": [], "userTaskId": "Activity_16yfpz5"}, {"status": "done", "otime": "2025-05-23 09:56:11", "formData": "[]", "ctime": "2025-05-23 09:56:10", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e6fa0793", "subProcessInstanceStepId": "", "memo": "自动通过!", "isExtraAssignee": false, "etime": "2025-05-23 09:56:11", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动设置机房上架实施人员", "consignors": [], "userTaskId": "Activity_1x5qr3d"}, {"status": "done", "otime": "2025-05-23 09:56:21", "formData": "[]", "ctime": "2025-05-23 09:56:10", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e6fa072f", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:56:21", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "网络实施", "consignors": [], "userTaskId": "Activity_0ookgnz"}, {"status": "done", "otime": "2025-05-23 09:56:43", "formData": "[{\"key\":\"h6onu0y2rd\",\"values\":[{\"dev_picture\":[{\"checksum\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"fileName\":\"文件上传测试.txt\",\"size\":0,\"instanceId\":\"\",\"source\":\"oss\"}]}]}]", "ctime": "2025-05-23 09:56:11", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e70978b2", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 09:56:43", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assigneeList", "taskName": "机房上架实施", "consignors": [], "userTaskId": "Activity_1x84cre"}, {"status": "revoked", "otime": "2025-05-23 10:03:14", "formData": "", "ctime": "2025-05-23 09:56:44", "toolStatus": "", "isSubStep": false, "instanceId": "635c3e9004c24", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "2025-05-23 10:03:14", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "申请人复核且监控安装实施", "consignors": [], "userTaskId": "Activity_1nfcs7h"}, {"status": "done", "otime": "2025-05-23 10:03:41", "formData": "[{\"key\":\"h6noiw9boh\",\"values\":[{\"h6noiw9boi\":\"\",\"h6noiw9boj\":[]}]},{\"key\":\"h6noiw9bol\",\"values\":[{\"user\":[{\"_impl_from\":[],\"_object_id\":\"USER\",\"_object_version\":24,\"_pre_ts\":**********,\"_ts\":**********,\"_version\":9,\"creator\":\"chenzhigang\",\"ctime\":\"2025-04-23 15:21:59\",\"deleteAuthorizers\":[],\"easyops_account_type\":\"normal\",\"instanceId\":\"6336cf4eefcee\",\"modifier\":\"miaofeng\",\"mtime\":\"2025-05-22 14:32:18\",\"name\":\"miao<PERSON>\",\"nickname\":\"苗峰\",\"org\":**********,\"readAuthorizers\":[],\"state\":\"valid\",\"team\":\"测试\",\"teamleader\":\"否\",\"updateAuthorizers\":[],\"user_email\":\"<EMAIL>\",\"user_tel\":\"***********\"}],\"department\":[],\"phone\":\"\",\"email\":\"\"}]},{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[],\"u_position\":\"\",\"itsc_uuid_\":\"h7lmt6gny0\"}]}]", "ctime": "2025-05-23 10:03:14", "toolStatus": "", "isSubStep": false, "instanceId": "635c400486f0d", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "2025-05-23 10:03:41", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "申请人", "consignors": [], "userTaskId": "Activity_0xw1mea"}, {"status": "done", "otime": "2025-05-23 10:03:54", "formData": "[{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[],\"u_position\":\"\",\"itsc_uuid_\":\"h7lmt6gny0\"}]}]", "ctime": "2025-05-23 10:03:41", "toolStatus": "", "isSubStep": false, "instanceId": "635c401dd154e", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 10:03:54", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "网络IP设置", "consignors": [], "userTaskId": "Activity_1l1at5c"}, {"status": "done", "otime": "2025-05-23 10:04:13", "formData": "[{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[{\"_impl_from\":[],\"_object_id\":\"_IDCRACK\",\"_object_version\":15,\"_pre_ts\":1744706104,\"_ts\":1747720989,\"_version\":3,\"code\":\"F8\",\"creator\":\"easyops\",\"ctime\":\"2025-04-14 10:00:46\",\"deleteAuthorizers\":[],\"instanceId\":\"632b36ba18195\",\"layout\":[{\"instanceId\":\"632ccebc402ff\",\"occupiedU\":2,\"startU\":36,\"type\":\"STORAGE_DEVICE@ONEMODEL\"},{\"instanceId\":\"632ccad865a33\",\"occupiedU\":1,\"startU\":39,\"type\":\"SWITCH@ONEMODEL\"},{\"instanceId\":\"6323ea42dbe1e\",\"occupiedU\":2,\"startU\":3,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea430faa3\",\"occupiedU\":2,\"startU\":33,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea430fe27\",\"occupiedU\":2,\"startU\":30,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4346735\",\"occupiedU\":2,\"startU\":24,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4348805\",\"occupiedU\":2,\"startU\":10,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4349b8d\",\"occupiedU\":2,\"startU\":16,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea434d5c1\",\"occupiedU\":2,\"startU\":21,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea434e2a5\",\"occupiedU\":2,\"startU\":7,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4373b6e\",\"occupiedU\":2,\"startU\":27,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea43764d6\",\"occupiedU\":2,\"startU\":18,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea437815a\",\"occupiedU\":2,\"startU\":13,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"}],\"location\":\"北环数据中心生产区F8机柜\",\"modifier\":\"chenzhigang\",\"mtime\":\"2025-05-20 14:03:09\",\"name\":\"BHSC1_F8\",\"org\":**********,\"readAuthorizers\":[],\"status\":\"启用\",\"type\":\"4-POST-封闭式机柜\",\"unum\":42,\"updateAuthorizers\":[]}],\"u_position\":\"1\",\"itsc_uuid_\":\"h7lmt6gny0\",\"asset_manage_group\":[]}]}]", "ctime": "2025-05-23 10:03:54", "toolStatus": "", "isSubStep": false, "instanceId": "635c402a6d045", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 10:04:13", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "机柜位置设置", "consignors": [], "userTaskId": "Activity_1npatnx"}, {"status": "done", "otime": "2025-05-23 10:04:30", "formData": "[]", "ctime": "2025-05-23 10:04:13", "toolStatus": "", "isSubStep": false, "instanceId": "635c403c50a7a", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 10:04:30", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动流转节点", "consignors": [], "userTaskId": "Activity_1becw28"}, {"status": "done", "otime": "2025-05-23 10:04:43", "formData": "[]", "ctime": "2025-05-23 10:04:30", "toolStatus": "", "isSubStep": false, "instanceId": "635c404c8ac19", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 10:04:43", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动节点1", "consignors": [], "userTaskId": "Activity_05ku7kw"}, {"status": "revoked", "otime": "2025-05-23 10:08:56", "formData": "", "ctime": "2025-05-23 10:04:43", "toolStatus": "", "isSubStep": false, "instanceId": "635c40591d8e5", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "2025-05-23 10:08:56", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "二级组审核", "consignors": [], "userTaskId": "Activity_0a25wz8"}, {"status": "done", "otime": "2025-05-23 10:09:30", "formData": "[{\"key\":\"h6noiw9boh\",\"values\":[{\"h6noiw9boi\":\"\",\"h6noiw9boj\":[]}]},{\"key\":\"h6noiw9bol\",\"values\":[{\"user\":[{\"_impl_from\":[],\"_object_id\":\"USER\",\"_object_version\":24,\"_pre_ts\":**********,\"_ts\":**********,\"_version\":9,\"creator\":\"chenzhigang\",\"ctime\":\"2025-04-23 15:21:59\",\"deleteAuthorizers\":[],\"easyops_account_type\":\"normal\",\"instanceId\":\"6336cf4eefcee\",\"modifier\":\"miaofeng\",\"mtime\":\"2025-05-22 14:32:18\",\"name\":\"miao<PERSON>\",\"nickname\":\"苗峰\",\"org\":**********,\"readAuthorizers\":[],\"state\":\"valid\",\"team\":\"测试\",\"teamleader\":\"否\",\"updateAuthorizers\":[],\"user_email\":\"<EMAIL>\",\"user_tel\":\"***********\"}],\"department\":[{\"_impl_from\":[],\"_object_id\":\"USER_GROUP\",\"_object_version\":4,\"_pre_ts\":**********,\"_ts\":**********,\"_version\":2,\"creator\":\"easyops\",\"ctime\":\"2025-03-10 16:41:57\",\"deleteAuthorizers\":[],\"instanceId\":\"62ff8f1f188ee\",\"modifier\":\"w00027\",\"mtime\":\"2025-05-20 14:42:54\",\"name\":\"信息技术总部网络安全组\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"phone\":\"\",\"email\":\"\"}]},{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[],\"u_position\":\"\",\"itsc_uuid_\":\"h7lmt6gny0\"}]}]", "ctime": "2025-05-23 10:08:56", "toolStatus": "", "isSubStep": false, "instanceId": "635c414a35f72", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "2025-05-23 10:09:30", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "申请人", "consignors": [], "userTaskId": "Activity_0xw1mea"}, {"status": "done", "otime": "2025-05-23 10:09:45", "formData": "[{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[],\"u_position\":\"\",\"itsc_uuid_\":\"h7lmt6gny0\"}]}]", "ctime": "2025-05-23 10:09:30", "toolStatus": "", "isSubStep": false, "instanceId": "635c416af3dfd", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 10:09:45", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "网络IP设置", "consignors": [], "userTaskId": "Activity_1l1at5c"}, {"status": "done", "otime": "2025-05-23 10:09:57", "formData": "[{\"key\":\"h6ol7po2o1\",\"values\":[{\"dev_name\":\"流程测试\",\"assetcode\":[{\"_impl_from\":[],\"_object_id\":\"ASSETS\",\"_object_version\":22,\"_ts\":**********,\"_version\":1,\"assetNumber\":\"************\",\"creator\":\"miaofeng\",\"ctime\":\"2025-05-23 08:27:50\",\"deleteAuthorizers\":[],\"instanceId\":\"635c2ab1365ed\",\"org\":**********,\"readAuthorizers\":[],\"updateAuthorizers\":[]}],\"dev_manager\":[],\"dev_manage_group\":[],\"supplier\":\"\",\"u_number\":\"1\",\"IP\":\"*******\",\"gateway\":\"*******\",\"rack\":[{\"_impl_from\":[],\"_object_id\":\"_IDCRACK\",\"_object_version\":15,\"_pre_ts\":1744706104,\"_ts\":1747720989,\"_version\":3,\"code\":\"F8\",\"creator\":\"easyops\",\"ctime\":\"2025-04-14 10:00:46\",\"deleteAuthorizers\":[],\"instanceId\":\"632b36ba18195\",\"layout\":[{\"instanceId\":\"632ccebc402ff\",\"occupiedU\":2,\"startU\":36,\"type\":\"STORAGE_DEVICE@ONEMODEL\"},{\"instanceId\":\"632ccad865a33\",\"occupiedU\":1,\"startU\":39,\"type\":\"SWITCH@ONEMODEL\"},{\"instanceId\":\"6323ea42dbe1e\",\"occupiedU\":2,\"startU\":3,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea430faa3\",\"occupiedU\":2,\"startU\":33,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea430fe27\",\"occupiedU\":2,\"startU\":30,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4346735\",\"occupiedU\":2,\"startU\":24,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4348805\",\"occupiedU\":2,\"startU\":10,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4349b8d\",\"occupiedU\":2,\"startU\":16,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea434d5c1\",\"occupiedU\":2,\"startU\":21,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea434e2a5\",\"occupiedU\":2,\"startU\":7,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea4373b6e\",\"occupiedU\":2,\"startU\":27,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea43764d6\",\"occupiedU\":2,\"startU\":18,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"},{\"instanceId\":\"6323ea437815a\",\"occupiedU\":2,\"startU\":13,\"type\":\"PHYSICAL_SERVER@ONEMODEL\"}],\"location\":\"北环数据中心生产区F8机柜\",\"modifier\":\"chenzhigang\",\"mtime\":\"2025-05-20 14:03:09\",\"name\":\"BHSC1_F8\",\"org\":**********,\"readAuthorizers\":[],\"status\":\"启用\",\"type\":\"4-POST-封闭式机柜\",\"unum\":42,\"updateAuthorizers\":[]}],\"u_position\":\"1\",\"itsc_uuid_\":\"h7lmt6gny0\",\"asset_manage_group\":[]}]}]", "ctime": "2025-05-23 10:09:45", "toolStatus": "", "isSubStep": false, "instanceId": "635c4178e22f0", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 10:09:57", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "机柜位置设置", "consignors": [], "userTaskId": "Activity_1npatnx"}, {"status": "done", "otime": "2025-05-23 10:10:06", "formData": "[]", "ctime": "2025-05-23 10:09:57", "toolStatus": "", "isSubStep": false, "instanceId": "635c4184c4012", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-23 10:10:06", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "自动流转节点", "consignors": [], "userTaskId": "Activity_1becw28"}]
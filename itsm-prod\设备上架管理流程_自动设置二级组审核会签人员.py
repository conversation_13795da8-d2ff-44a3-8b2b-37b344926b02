#!/usr/local/easyops/python/bin/python
# --*--coding:utf8--*-- 
import requests
import json 

HOST = EASYOPS_CMDB_HOST.split(":")[0]

# 从 CMDB 中检索实例数据
def cmdb_instance_search(object,params={}):
    # 构建搜索实例的 URL
    result = []
    url = "http://{}/v2/object/{}/instance/_search".format(HOST,object)
    headers = {
        'org': str(EASYOPS_ORG),  # 组织 ID
        'user': 'easyops',  # 用户名（用于认证）
        'Content-Type': 'application/json',  # 请求的内容类型为 JSON
        "host": "cmdb_resource.easyops-only.com"  # 目标主机地址
    }
    
    ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
    
    if ret["code"] == 0:
        if ret["data"]["list"]:
            result =  ret["data"]["list"]
    else:
        print ret
    return result  # 返回所有符合条件的实例数据


if __name__ == "__main__":
    """
    1、将orderInfo字符串转换为Python对象，以便后续处理
    2、从orderInfo对象中提取出创建人（creator）字段的值,creator为提单人
    3、查询USER用户模型中name(用户名)等于creator的记录
    4、cmdb_instance_search("USER", params=group_params) 会返回一个列表，然后取出第一个元素，
    并将其赋值给user_group变量
    5、从user_group中提取出team所属团队的值，赋值给group_name变量
    6、从USER_GROUP用户组模型中查询组名称等于group_name的记录，并返回用户名和团队负责人字段
    _members为用户组模型和用户模型的关系ID,_members.name是用户名
    _members.teamleader是团队负责人
    7、遍历查询结果，判断团队负责人字段是否等于"是"，如果是，则将用户名添加到user_list列表中
    8、将user_list列表中的用户名用逗号连接成字符串，并赋值给users_str变量
    9、将users_str字符串赋值给assigneeUser字段
    10、调用PutStr内置函数，将字符串传入"assigneeUser"字段
    assigneeUser为输出定义中的输出参数ID，对应输出参数标题为"二级组审核会签人员"
    说明：PutStr为内置函数，工具的输出，为用于流程传递值；需要先在"输出定义"中的"输出列"中先声明对应变量
    """
    user_list = []
    orderInfo = json.loads(orderInfo)
    creator = orderInfo["creator"]
    # 调试
    print json.dumps(orderInfo, indent=2, ensure_ascii=False)
    print orderInfo["creator"]    
    group_params = {"fields":{"*": 1},"query":{"name":{"$eq":creator}}}
    user_group = cmdb_instance_search("USER", params=group_params)[0]
    # 调试
    print user_group
    group_name = user_group.get("team")
    params = {"fields":{"_members.name":1,"_members.teamleader":1},"query":{"name":{"$eq": group_name}}}
    users = cmdb_instance_search("USER_GROUP", params=params)
    for user in users[0]["_members"]:
        if user.get("teamleader") == u"是":
            user_list.append(user.get("name"))

    print(user_list)
    
    users_str = ",".join(user_list)
    print(users_str)
    PutStr("assigneeUser", users_str)
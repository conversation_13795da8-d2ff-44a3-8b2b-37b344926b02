#!/usr/local/easyops/python/bin/python
# --*--coding:utf8--*-- 
import requests
import json 

HOST = EASYOPS_CMDB_HOST.split(":")[0]
orderInfo = json.loads(orderInfo)

# 从 CMDB 中检索实例数据
def cmdb_instance_search(object,params={}):
    # 构建搜索实例的 URL
    result = []
    url = "http://{}/object/{}/instance/_search".format(HOST,object)
    headers = {
        'org': str(EASYOPS_ORG),  # 组织 ID
        'user': 'easyops',  # 用户名（用于认证）
        'Content-Type': 'application/json',  # 请求的内容类型为 JSON
        "host": "cmdb_resource.easyops-only.com"  # 目标主机地址
    }
    
    #params = {"fields":{"isntanceId":True},"query":{"ip":{"$eq":ip}}}
    ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
    
    if ret["code"] == 0:
        if ret["data"]["list"]:
            result =  ret["data"]["list"]
    else:
        print ret
    return result  # 返回所有符合条件的实例数据

def get_department():
    department = []
    print json.dumps(orderInfo["stepList"], ensure_ascii=False, indent=2)
    for step in orderInfo["stepList"]:
        # 1 修改
        if step["userTaskId"] == "Activity_0xw1mea":
            formData = json.loads(step["formData"])
            # print json.dumps(formData,indent=2,ensure_ascii=False)
            for i in formData:
                # 2 修改
                if i["key"] == 'h6noiw9bol':
                    # department.append(i["values"][0]["department"])
                    for x in i["values"][0]["department"]:
                        department.append(x["name"])
                        

    return list(set(department))
    
if __name__ == '__main__':
    ret = get_department()
    print json.dumps(ret,indent=2,ensure_ascii=False)
    group_params = {"fields":{"_members.name":1,"_members.teamleader":1},"query":{"name":{"$in":ret}}}
    group_ret = cmdb_instance_search("USER_GROUP",params=group_params)
    #print json.dumps(group_ret,indent=2,ensure_ascii=False)
    teamleaders = []
    if group_ret:
        for i in group_ret:
            for x in i["_members"]:
                if x.get("teamleader") == u"是":
                    teamleaders.append(x["name"]) 
    print teamleaders
    users_str = ",".join(teamleaders)
    PutStr("assigneeList",users_str)
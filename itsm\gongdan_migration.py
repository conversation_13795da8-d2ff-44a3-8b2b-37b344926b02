#--*--coding:utf8--*--
import sys,os,json,requests,datetime
from pymongo import MongoClient
from bson import ObjectId
import logging
import inspect
import pytz
from urllib import quote
import re
import base64
import requests
from bs4 import BeautifulSoup
import random
import string
import time
import functools
import pymysql
from concurrent.futures import ThreadPoolExecutor, as_completed

reload(sys)
sys.setdefaultencoding("utf8")

# 日志记录模块
def log_record(**log_dic):
	# caller_name = inspect.stack()[1].function #python3
	caller_name = "Caller function name:", inspect.stack()[1][3]

	# log_file = "app.log"
	logging.basicConfig(
		filename=log_file,
		level=logging.ERROR,
		format="%(asctime)s - %(levelname)s - %(message)s"
	)

	log_infor1 = []
	for k, v in log_dic.items():
		if isinstance(k, unicode):
			k = k.encode('utf-8')
		elif isinstance(v, unicode):
			v = v.encode('utf-8')
		log_infor1.append("{}: {}".format(k, v))
	log_infor1 = " -  ".join(log_infor1)
	log_infor2 = "%s 时出错：%s" %(caller_name,log_infor1)
	print "%s" %(log_infor2)

	logging.error("%s", log_infor2)

# 处理异常的json数据
class DateEncoder(json.JSONEncoder):
	def default(self, obj):
		# 将json中的日期格式转换为字符串
		if isinstance(obj,datetime.datetime):
			return obj.strftime("%Y-%m-%d %H:%M:%S")
		# 将json中的ObjectId转换为子字符串
		elif isinstance(obj, ObjectId):
			return str(obj)
		else:
			return json.JSONEncoder.default(self,obj)

# 判定时间数据是否为指定的日期格式。
def is_valid_format(date_string, date_format):
	try:
		datetime.datetime.strptime(date_string, date_format)
		return True
	# except ValueError:
	except Exception, e:
		return False

# 时间字段的时区转换
def convert_timezone(date_str):
	if is_valid_format(date_str, '%Y-%m-%d %H:%M:%S'):
		naive_date = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
		# 假设原始日期是 UTC 时间
		utc_date = pytz.utc.localize(naive_date)

		# 转换为 UTC+8 时区
		target_timezone = pytz.timezone('Asia/Shanghai')
		local_date = utc_date.astimezone(target_timezone)
		print u"convert_timezone result is: %s,转换成功" % (local_date.strftime('%Y-%m-%d %H:%M:%S'))
	else:
		local_date = date_str
		log_info = "convert_timezone result is: %s,转换失败，保持原数据输出" %(local_date)
		log_record(log_info=log_info)

	# 输出结果
	return local_date

# 时间字段的格式转换
def convert_datetime(original_date_str):
    # 原始日期字符串
	# original_date_str = "2020-09-25S06:43:41.105Z"

	parsed_date = original_date_str
	data_type = type(original_date_str)
	if is_valid_format(original_date_str, "%Y-%m-%dT%H:%M:%S.%fZ"):
		# 按指定格式解析为 datetime 对象
		dt = datetime.datetime.strptime(original_date_str, "%Y-%m-%dT%H:%M:%S.%fZ")
		parsed_date = dt.strftime("%Y-%m-%d %H:%M:%S")
	elif is_valid_format(original_date_str, "%Y-%m-%d %H:%M:%S.%f"):
		dt = datetime.datetime.strptime(original_date_str, "%Y-%m-%d %H:%M:%S.%f")
		parsed_date = dt.strftime("%Y-%m-%d %H:%M:%S")
	elif is_valid_format(original_date_str, "%Y-%m-%d %H:%M:%S:%f"):
		# 按指定格式解析为 datetime 对象
		dt = datetime.datetime.strptime(original_date_str, "%Y-%m-%d %H:%M:%S:%f")

		# 格式化为目标格式
		parsed_date = dt.strftime("%Y-%m-%d %H:%M:%S")

	elif data_type == long or data_type == int:
		timestamp_sec = original_date_str/1000.0
		# 转换为 datetime 对象
		dt = datetime.datetime.fromtimestamp(timestamp_sec)

		# 格式化为日期字符串
		parsed_date = dt.strftime("%Y-%m-%d %H:%M:%S")

	# 输出格式化结果
	# print("parsed_date: ",parsed_date)
	return parsed_date

def replace_dict_value(dict1, dict2):
	data = {}
	dict1_value = dict1.values()[0]
	dict1_key = dict1.keys()[0]

	if isinstance(dict1_value, list):
		list_data = []
		for dict2_key in dict2.keys():
			for dict1_valu in dict1_value:
				if dict2_key == dict1_valu:
					list_data.append(dict2[dict2_key])
		if list_data:
			data[dict1_key] = list_data
		else:
			data = dict1
	elif isinstance(dict1_value, int) :
		for dict2_key in dict2.keys():
			if int(dict2_key) == int(dict1_value):
				data[dict1_key] = dict2[dict2_key]
				break
	elif isinstance(dict1_value, str) or isinstance(dict1_value, unicode):
		for dict2_key in dict2.keys():
			if dict2_key == dict1_value:
				data[dict1_key] = dict2[dict2_key]
				break
	elif dict1_value == None:
		data[dict1_key] = None
	else:
		executive_result["replace_dict_value_fairure"] +=1
		log_info = "replace_dict_value %s: 不是列表，也不是字符串"  %(dict1_value)
		log_record(log_info=log_info,flowNo=flowNo)

	if not data:
		data = dict1
	print "replace_dict_value result is : %s" %(data)
	return data

# mysql 数据处理
def mysql_result_deal(mysql_result,res_name):
	try:
		result = []
		for i in mysql_result:
			# i = {u'res_name': u'10.3.41.112-hadoop1.cbas', u'data': None, u'sandboxId': None}
			if i['res_name'] == res_name:
				print "mysql_result_deal is: %s" %(i)
				if isinstance(i, dict):
					result.append(i['data'])
				elif isinstance(i, str):
					result.append(json.loads(i['data']))
				else:
					print "mysql_result data type is: %s,无法处理。" %(type(i))

		# print "mongo_relevance_mysql result is: %s" % (result)
		return result
	except Exception, e:
		log_record(e=e)

# mongo数据与mysql数据关联
def mongo_relevance_mysql(data1):
	try:
		result = []
		mongo_result_value = data1

		for item in mongo_result_value:
			if isinstance(item, dict) and 'name' in item and "url" not in item:
				res_name = item["name"]
				print "res_name is: %s" %(res_name)
				if res_name is not None and mysql_result:
					result += mysql_result_deal(mysql_result,res_name)
			else:
				result = mongo_result_value

		print "mongo_relevance_mysql result is: %s" %(result)
		return result
	except Exception, e:
		log_record(e=e)


def Attachment_data_deal(mongo_result, uv_keys):
	try:
		data={}
		for uv_key, uv_value in uv_keys.items():
			if uv_key in mongo_result:
				mongo_result_value = mongo_result[uv_key]
				if uv_value and mongo_result_value:
					mongo_result_value = json.loads(mongo_result_value)
					item_list = []
					for item in mongo_result_value:
						item_list += item[uv_value]
					data[uv_key] = item_list
				else:
					data[uv_key] = mongo_result_value
			# 从mongo中查找附件
			else:
				ticketId = mongo_result['ticketId']
				# 提取 fileName 和 fileId
				result = []
				for item in mongo_LinkedAttach:
					if item['ticketId'] == ticketId:
						if not 'files' in item:
							break
						for file_info in item['files']:
							if file_info['fieldCode'] == uv_key:
								result.append({
									'name': file_info['fileName'],
									'id': file_info['fileId']
								})

				if result :
					data[uv_key] = result
					print "Attachment_data_deal result is ok"
				else:
					print "Attachment_data_deal result is: %s is does not exist" % (uv_key)

		return data

	except Exception, e:
		executive_result["Attachment_data_deal"] +=1
		log_record(e=e)

# 处理mongo数据
def mongo_data_deal(mongo_result, uv_keys=[]):
	try:
		# 使用自定义的cls方法处理mongo_result中的datetime、ObjectId数据
		mongo_result.update(mongo_result["formData"])
		mongo_result = json.dumps(mongo_result,cls=DateEncoder,ensure_ascii=False)
		mongo_result = json.loads(mongo_result)

		data={}
		uv_keys = mongo_result.keys()

		# 时间数据处理
		datetime_list = ["updateTime", "createTime"]
		for datetime_key in datetime_list:
			datetime_value = convert_datetime(mongo_result[datetime_key])
			mongo_result[datetime_key] = convert_timezone(datetime_value)

		# 取出指定key
		# uv_keys = [u'applicant']
		for uv_key in uv_keys:
			mongo_result_value = mongo_result[uv_key]

			# k/v值转换
			replace_list = ["intreport","score","priority","kind","reportyear","reportmonth","status","urgentLevel","testResult","modelId","jinjibiangeng","yingxiangdu","chgResult","changerdepartment","creator","executor","zuoye","reporttyppe","repperiod","applicant","Applicationmode"]

			if uv_key.encode('utf-8') in replace_list:
				dict1 = {}
				dict1[uv_key] = mongo_result_value
				replace_result = replace_dict_value(dict1,globals()[uv_key])
				data.update(replace_result)

				if uv_key == "creator":
					data["creator_uv"] = data.pop("creator")
			elif uv_key is None:
				data[uv_key] = ""
			# 关联mysql 中的配置项数据
			elif uv_key == "ipaddr":
				data["IPList"] =  mongo_result_value
			elif uv_key in ["ywnxssr","zhuceziben"]:
				data[uv_key] = str(mongo_result_value) + "万元"
			elif isinstance(mongo_result_value,list):
				if mongo_result_value:
					list_result = mongo_relevance_mysql(mongo_result_value)
					if list_result:
						data[uv_key] = list_result
						continue
			else:
				data[uv_key] = mongo_result_value

		print "mongo_data_deal result is ok"
		return data
	except Exception, e:
		executive_result["mongo_input_uv_failure"] +=1
		log_record(e=e)

# 数据上传
def post_data(data,url,unique_key):
	try:
		payload = json.dumps({
		  "keys": [
			unique_key
		  ],
		  "datas": [
			data
		  ]
		},cls=DateEncoder)
		# print("payload is: ",payload)
		headers = {
		  'org': '2024021401',
		  'user': 'easyops',
		  'Content-Type': 'application/json',
		  # 'host': 'cmdb_resource.easyops-only.com'
		}
		response = requests.request("POST", url, headers=headers, data=payload)
		result = response.text
		# if json.loads(result)["data"]["failed_count"] != 0:
		# 	print u"post_data 时出错: - payload: %s  - title: %s" % (payload, title)
		print "post_data result is: %s" %(result)
		return result

	except Exception, e:
		executive_result["post_data_failure"] +=1
		log_record(e=e)
		print "past_data 时出错: ", e

# 从优云平台获取处理记录
def getProcessRecord(host,cookie,ticketId):
	try:
		url = "http://%s/itsm/api/v2/ticket/getProcessRecord/%s" %(host,ticketId)
		payload = ""
		headers = {
			'cookie': cookie,
			'Authorization': 'Bearer 3de187b1402f38120ff7aacfdc8ba477e36efed5cdd0af879c0cdc8d57e44ab5'
		}

		response = requests.request("GET", url, headers=headers, data=payload)
		if response.status_code != 200:
			response_content = response.content
			log_record(ticketId=ticketId,response_content=response_content,title=title)
			executive_result["getProcessRecord_failure"] += 1
			return

		data = []
		processRecord = json.loads(response.text)["data"]["processRecord"]

		# print "processRecord: %s" %(processRecord)
		for item in processRecord:
			data_dic = {}
			if "exectorName" in item and "actionType" in item and "toUser" in item:
				data_dic["exectorTime"] = convert_datetime(item["exectorTime"])
				data_dic["activityName"] = item["activityName"]
				data_dic["exectorName"] = item["exectorName"]
				data_dic["actionType"]  = item["actionType"]
				data_dic["toUser"]  = item["toUser"]
				data_dic["ticketAdvice"] = item["ticketAdvice"]
			else:
				log_info = "该数据为差异数据，无法检索到所需key"
				log_record(log_info=log_info,title=title,flowNo=flowNo)

				continue

			exectorName = data_dic["exectorName"]
			if not exectorName:
				exectorName = u"系统"
			toUser = data_dic["toUser"]
			actionType = data_dic["actionType"]

			if actionType == u"创建":
				content = u"%s 创建了工单给 %s" %(exectorName,toUser)
			elif actionType == u"接单":
				content = u"%s 接单" %(exectorName)
			elif actionType == u"完成":
				content = u"%s 完成了工单" %(exectorName)
			elif actionType == u"提交":
				content = u"%s 提交了工单给 %s" %(exectorName,toUser)
			elif actionType == u"回退":
				content = u"%s 回退了工单给 %s" %(exectorName,toUser)
			elif actionType == u"关闭":
				content = u"%s 关闭了工单" %(exectorName)
			elif actionType == u"改派":
				content = u"%s 改派了工单给 %s" %(exectorName,toUser)
			elif actionType == u"取回":
				content = u"%s 取回了工单" %(exectorName)
			elif actionType == u"重开":
				content = u"%s 重开了工单" %(exectorName)
			elif actionType == u"废除":
				content = u"%s 废除了工单" %(exectorName)
			elif actionType == u"恢复":
				content = u"%s 恢复了工单" %(exectorName)
			elif actionType == u"挂起":
				content = u"%s 挂起了工单" %(exectorName)
			elif actionType == u"更新":
				content = u"%s 更新了工单" %(exectorName)
			else:
				log_info = "不匹配的动作类型 %s" % (actionType)
				log_record(log_info=log_info,flowNo=flowNo,title=title)
				executive_result["getProcessRecord_failure"] += 1
			data_dic["content"] = content
			data.append(data_dic)
		# print "getProcessRecord result is : %s" %(data)
		return data
	except Exception, e:
		log_record(e=e,flowNo=flowNo,title=title)
		executive_result["getProcessRecord_failure"] += 1

def import_batch_decorator(batch=300):
	def decorator(func):
		@functools.wraps(func)
		def wrapper(datas):
			try:
				for position in range(0, len(datas), batch):
					end_position = position + batch
					ret = datas[position:end_position]  # 获取当前批次的数据
					print "______________  BEGINING position is: %d - %d   ________________" % (position, end_position)
					func(ret)
					print "______________  END position is: %d - %d   ________________" % (position, end_position)

			except Exception as e:
				print("import_batch_decorator: {}".format(e))
		return wrapper
	return decorator

# 线程池装饰器
def thread_pool_decorator(max_workers=5):
	def decorator(func):
		@functools.wraps(func)
		def wrapper(data_list):
			result = []
			# 创建线程池
			with ThreadPoolExecutor(max_workers=max_workers) as executor:
				try:
					start = time.time()
					# 提交任务到线程池
					futures = [executor.submit(func, item) for item in data_list]

					# 等待任务完成并收集结果
					# future维护了线程的生命周期，因此这里并没有调用join等待。
					result = [future.result() for future in as_completed(futures)]

					# 返回 future 对象，以便调用者可以获取结果
					count_time = time.time() - start
					print "thread_pool spend time is: %s" %(count_time)
					return result
				except Exception as e:
					print("thread_pool_decorator: {}".format(e))
		return wrapper
	return decorator

import io
# 从优云平台下载附件
@thread_pool_decorator(max_workers=5)
def getAttachFile(files_list):
	try:
		file = files_list
		file_uid = file["id"]
		file_name = file["name"]
		path = "%s/%s" %(file_path,file_name)
		url = "http://%s/itsm/api/v2/file/downloadFile/%s/%s" %(youy_host,file_uid,file_name)
		payload = ""
		headers = {
		  'cookie': cookie,
		  'Authorization': 'Bearer 3de187b1402f38120ff7aacfdc8ba477e36efed5cdd0af879c0cdc8d57e44ab5'
		}

		response = requests.request("GET", url, headers=headers, data=payload)
		response.raise_for_status()  # 如果请求失败，抛出异常

		with open(path, "wb") as f:
			f.write(response.content)

		file_size = os.path.getsize(path)
		result = {}
		result[path] = int(file_size)
		print "getAttachFile_tmp result is: 文件已成功下载并保存为 %s, size is: %s" %(file_name,file_size)
		return result

	except requests.exceptions.RequestException, e:
		executive_result["getAttachFile_failure"] += 1
		log_record(e=e,flowNo=flowNo)
		path = ""
		return path
	except Exception, e:
		log_record(e=e, flowNo=flowNo)
		executive_result["getAttachFile_failure"] += 1
		path = ""
		return path

# 上传附件到优维平台
@thread_pool_decorator(max_workers=5)
def upload_file(fileinfo):
	try:
		if not fileinfo:
			return ""
		data = ""
		# 将附件信息统一放到data中，这意味着不论该附件来自于那个字段，都会存放在一个附件字段里面
		url = "http://{}:8138/api/v1/objectStore/bucket/cmdb-bucket/object".format(uv_host)
		headers = {"user": "easyops", "org": str(EASYOPS_ORG)}
		filepath = fileinfo.keys()[0]
		filesize = fileinfo.values()[0]
		filename = filepath.split('/')[-1]
		# filename = filename[:30]
		# encoded_filename = quote(filename.encode('utf-8'))
		# files = {"file": open(filepath, "rb")}
		file = {'file': (filename, open(filepath, 'rb'))}

		response = requests.put(url, headers=headers, files=file)
		# print "upload_file result is: ", response.text
		if response.status_code == 200:
			objectName = response.json()["data"]["objectName"]
			objectUrl = "api/gateway/object_store.object_store.GetObject/api/v1/objectStore/bucket/cmdb-bucket/object/" + objectName
			name = os.path.basename(filepath)
			data = {"name": name, "url": objectUrl,"size":filesize, "type":""}
		else:
			response_content = response.content
			log_record(flowNo=flowNo,response_content=response_content)
			executive_result["upload_file_failure"] +=1
			return	data
		print "upload_file reuslt is: %s" %(data)
		return data
	except Exception, e:
		log_record(e=e,flowNo=flowNo)
		executive_result["upload_file_failure"] +=1


# 获取模型id与名称的对照表
def get_modelId(file_path):
	path = "%s/relateModels" %(file_path)
	with open(path, "r") as file:
		file_data = json.loads(file.read())
		relatemodels = file_data['data']['relateModels']
		data = {}
		for relatemodel in relatemodels:
			id = relatemodel["id"]
			name = relatemodel["name"]
			data[id] = name
	print "get_relateModels result is: ", data
	return data

# 获取用户id与名称的对照表
def get_userinfo(file_path):
	path = "%s/userinfo" %(file_path)
	with open(path, "r") as file:
		data = json.loads(file.read())

	print "get_userinfo is: ",data
	return data

# 从mongo获取工单数据
def mongo_find(table,id):
	sql_position = 0
	sql_limit = 10000
	connection_string = "**********************************************"
	client = MongoClient(connection_string)
	db = client["itsm"]
	collection = db[table]
	# documents = collection.find(id).limit(sql_limit)
	documents_count = collection.find(id).count()
	print "documents_count is: %d" %(documents_count)
	documents = collection.find(id).skip(sql_position).limit(sql_limit)

	# for document in documents:
	#     print(document)
	# 将查询结果存放到列表中，这样便于之后分批处理
	data_list = []
	for document in documents:
		data_list.append(document)

	return data_list

# 从mysql获取配置项数据。
def mysql_find():
	connection = pymysql.connect(
		host='**********',
		port=3306,
		user='dbuser',
		passwd='DBuser123!',
		db = 'hornet',
		charset='utf8',
		cursorclass=pymysql.cursors.DictCursor  # 使用字典游标
	)

	try:
		with connection.cursor() as cursor:
			# SQL 查询语句
			# sql = "select data,sandboxid from hnt_tk_res_relation where sandboxid='677b9f362f1294ec593e6535';"
			# sql = "select data from hnt_tk_res_relation where sandboxid = '%s';" %(sandboxId)
			# sql = "select data from hnt_tk_res_relation where res_name = '%s';" % (res_name)
			sql = "select sandboxId,res_name,data from hnt_tk_res_relation"
			cursor.execute(sql)  # 执行查询，传递参数

			# 获取查询结果
			result = cursor.fetchall()

			# if result:
			# 	print "mysql_find result is: %s" %(result)  # 打印结果
			return result
	except Exception, e:
		log_record(e=e)
	finally:
		# 关闭数据库连接
		connection.close()

# 定义生成随机字符串的函数
def generate_random_string(length):
    # 从字母和数字中随机选择字符
    characters = string.ascii_letters + string.digits  # 包含大小写字母和数字
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string

# 从富文本中提取出截图数据
def image_base64_deal(html_content,file_path):
	# 假设你有一个包含 <p> 标签的 HTML 内容
	# html_content = """
	# <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."></p>
	# """

	# 使用 BeautifulSoup 解析 HTML
	soup = BeautifulSoup(html_content, 'html.parser')

	# 查找所有的 <p> 标签
	p_tags = soup.find_all('p')
	data = []
	for p in p_tags:
		# 查找 <img> 标签
		img_tags = p.find_all('img')
		# 确保该imag标签里有src属性，使能获取到值
		for img_tag in img_tags:
			if img_tag and 'src' in img_tag.attrs:
				image_name = img_tag['alt']
				# 使用 rsplit 去除 .png 后缀
				image_prefix = image_name.rsplit('.', 1)[0]
				image_extension = image_name.rsplit('.', 1)[1]
				random_string = generate_random_string(7)
				image_name = "%s-%s.%s" % (image_prefix, random_string,image_extension)
				src = img_tag['src']
				# 使用正则表达式提取 Base64 数据
				match = re.match(r'data:image/(\w+);base64,(.*)', src)
				if match:
					image_type, base64_data = match.groups()
					path = "%s/%s" % (file_path, image_name)
					# 解码 Base64 数据
					image_data = base64.b64decode(base64_data)
					# 保存图片到本地
					with open(path, 'wb') as f:
						f.write(image_data)
					print u"image_base64_deal result is : 图片已保存为 %s"  %(path)
					data.append(path)

	return data

# 环境判断模块，在window、linux上给予不同的执行参数
class environ_judge:
	def my_environ_judge(self,file_prefix):
		self.file_prefix = file_prefix
		# 判断系统类型
		if sys.platform.startswith('win'):
			print "# Windows 系统"
			print u"当前 Python 版本: %s" % (sys.version)

			# 奇怪的是在window运行，文件依然被下载到了E:\tmp 下
			self.file_path = "AttachFile"
			self.log_file = "%s.log" % (self.file_prefix)

		elif sys.platform.startswith('linux') or sys.platform.startswith('darwin'):
			print "# Unix/Linux 或 macOS 系统"
			print u"当前 Python 版本: %s" % (sys.version)
			self.file_path = "/data/gongdan/"
			self.log_file = "%s/%s.log" % (self.file_path, self.file_prefix)

		else:
			print "# 其他系统"

# 图片下载模块
def get_imag(files_value):
	data = []
	if isinstance(files_value, str) or isinstance(files_value, unicode):
		if files_value.strip().startswith('<p>') and files_value.strip().endswith('</p>'):
			path_list = image_base64_deal(files_value, file_path)
			result = {}
			for path in path_list:
				file_size = os.path.getsize(path)
				result[path] = int(file_size)
				data.append(result)

	elif files_value is None:
		pass
	else:
		log_info = u"FILES_data 异常：无法处理该下载文件  %s" % (flowNo)
		print "files_value is: %s" %(files_value)
		log_record(log_info=log_info, flowNo=flowNo)
		executive_result["FILES_data"] += 1

	return data

# 主进程
@import_batch_decorator(batch=300)
def main_process(mongo_shard):
	global title
	global flowNo
	global ticketId
	global youy_host
	global cookie
	global EASYOPS_ORG

	for mongo_result in mongo_shard:
		executive_result["mongo_result_count"] += 1
		title = mongo_result['title']
		flowNo = mongo_result['flowNo']
		ticketId = mongo_result["ticketId"]
		print "______________  FLOWNO %s BEGINING    ________________" % (flowNo)

		# 处理mongo数据
		model_data = mongo_data_deal(mongo_result)

		if not model_data:
			return

		youy_host = "**********"
		cookie = "token=3de187b1402f38120ff7aacfdc8ba477e36efed5cdd0af879c0cdc8d57e44ab5; language=zh_CN; skin=white"
		EASYOPS_ORG = 2024021401

		if isinstance(FILES_list, dict):
			FILES_data = Attachment_data_deal(model_data, FILES_list)

			# 从优云平台下载文件
			files_list = []
			global upload_imag_list
			upload_imag_list = []
			for files_key, files_value in FILES_data.items():
				if isinstance(files_value, list):
					files_list += files_value
				else:
					# 图片数据下载
					get_imag_result = get_imag(files_value)
					upload_imag_list += get_imag_result

			# 数据上传至优维平台
			upload_file_list = getAttachFile(files_list)
			upload_list = upload_file_list + upload_imag_list

			file_list = upload_file(upload_list)
			model_data["file"] = file_list

		# 获取处理记录
		ProcessRecord = getProcessRecord(youy_host, cookie, ticketId)
		model_data["PROCESSRECORD"] = ProcessRecord

		# 请求接口，上传数据到优维平台
		url = "http://%s:8079/object/%s/instance/_import" % (uv_host, model)
		unique_key = "flowNo"
		post_result = post_data(model_data, url, unique_key)
		if json.loads(post_result)["data"]["failed_count"] == 0:
			executive_result["post_data_count"] += 1
		else:
			log_record(flowNo=flowNo, title=title)
			return

		print "______________  FLOWNO %s END   ________________" % (flowNo)

# 声明工单附件字段
CHANGE_AND_RELEASE_FILES = {
	'file': '',
	"bakcontent": '',
	'BGSSJG': '',
	'yjandhuitui': ' ',
	"changesolution": ''
}

BUSINESS_SYSTEM_ONLINE_FILES = {
	"wlxgzl":"",
	'aqxgzl':"",
	'ywxtsszl':"",
	"ywxgzl":"",
	"qianpi":"",
	"zzsmjg":"",
	"aqshyj":"",
	"file":"",
	"hgbmyj":"",
	"fxshyj":"",
	"importYwfile":"",
	"OtherInformation":""
}

TASK_MANAGEMENT_PROCESS_FILES = {
	"file":""
}

EVENT_ORDER_FILES = {
	"incidentpic": "",
	"file": ""
}

NOTIFICAITON_ANNOUNCEMENT_FILES = {
	"file": ""
}

VPN_RESOURCE_APPLY_FILES = {
	"lsbg": ""
}

NET_WORK_RESOURCE_REQUEST_FILES = {
	"file":""
}

OFFICE_INTERNET_ANTIVIRUS_REPORT_FILES = {
	"file": ""
}

APPLY_EXTRA_WORK_FILES = {
	"file": "",
	"jbfj": "",
}

TASK_MANAGE_SUBPROCESS_FILES = {
	"file": "",
	'distSolvent': 'files',
}

LDSM_FILES  = {
	"file":"",
	"AdditionalFiles":"",
	"picresult":"",
	'distSolvent': 'files',
}

REPORT_CHANNEL_FILES = {
	"picresult":"",
	"file":"",
	"distSolvent": "files"
}

RESOURCE_ASSET_MANAGEMENT_PROCESS_FILES = {
    # 附件
    "zj":"",
    "jfsbcrdjtp":"",
    "file":""
}

BUSINESS_SYSTEM_REQUEST_FILES = {
	# 附件
	"file":""
}

DEVICE_PUTAWAY_PROCESS_FILES = {
	# 附件
	"file": ""
}

DEVICE_UNSHELVE_PROCESS_FILES = {
	# 附件
	"file": ""
}

ASSET_RETIREMENT_PROCESS_FILES = {
	"file": "",
	"AdditionalFiles": "",
	"screenshotpic": "",
}

ASSET_STORAGE_PROCESS_FILES = {
	"file": ""
}


BASTION_HOST_APPROVE_FILES = {
	"file":"",
}

TW_FILES = {
	"file":"",
}

RISK_FILES = {
	"RiskPic":"",
	"file": "",
}

JFAZKNSL_FILES = {
	"fjshangchuan":"",
	"file": "",
	"alal": "",
	"xxjsal": "",
}

YDGZZJTZ_FILES = {
	"bgmb":""

}

ASTBG_FILES = {
	"file":"",
}

PROBLEM_FILES = {
	"picguoc":"",
	"incidentpic":"",
	"picguoc":"",
	"picresult":"",
	"file":"",
}

JIEJEUFA_FIELS = {
	"file": "",
}

SELF_FILES = {
	"file": "",
	"AdditionalFiles": "",
	"DetailDesc": ""
}

JIEJEUFA_FILES = {
	 "file": "",
}

XMZS_FILES = {
	"SigData": "",
	"ContractData": "",
	"AccessInformation": "",
	"FirstData": "",
	"SecondData": "",
	"ThirdPayData": "",
	"TenderInfo": "",
}

BXX_FILES = {
	"Offlinescheme": "",
	"ywbps": "",
	"OffineReport": "",
}

SORTLOOM_FILES = {
	"file": "",
}

# 要替换的字段值
jinjibiangeng = {
	"1": "是",
	"2": "否"
}

changerdepartment ={
    '1': '网络安全组',
    '2': '交易运营组',
    '3': '交易运营二组',
    '4': '非现场交易组',
    '5': '管理系统组',
    '6': '技术管理组',
    '7': '规划发展组',
    '8': '分支机构组'
}

testResult ={
	"0": "成功"
}

Applicationmode = {
	"1": "申请",
	"2": "修改",
	"3": "撤销",

}

yingxiangdu = {
	"1": "高",
	"2": "中",
	"3": "低",
}


kind = {
	"1": "故障",
	"2": "咨询",
	"3": "业务处理",
	"4": "建议",
	"5": "系统操作",
	"6": "流程方案",
	"7": "其他",
	"8": "机房巡检",
}

priority = {
	"1": "极高(1小时)",
	"2": "高(4小时)",
	"3": "中(1天)",
	"4": "中(1天)",
	"5": "很低(7天)",
}

score = {
	"0": "非常满意",
	"1": "满意",
	"2": "一般",
	"3": "不满意",
	"4": "非常不满意",
}

reportyear = {
	"1": "2018",
	"2": "2019",
	"3": "2020",
	"4": "2021",
	"5": "2022",
	"6": "2023",
	"7": "2024",
	"8": "2025",
}

reportmonth = {
	"1": "一月",
	"2": "二月",
	"3": "三月",
	"4": "四月",
	"5": "五月",
	"6": "六月",
	"7": "七月",
	"8": "八月",
	"9": "九月",
	"10": "十月",
	"11": "十一月",
	"12": "十二月",
}

repperiod = {
	"5a5f859d-ab26-4eac-8be2-55031227dd0b;ac2b2104-3858-4ef1-ba09-40fbcc995882": "上半年,7月",
	"6c897fec-ea07-460e-b5d6-3b34940613b8;6b1515c0-57ec-4a16-a209-19d5dd2b7f3e": "一季度，4月",
	"881a4f82-de58-4f9e-ac0a-df4002a22273;6b1515c0-57ec-4a16-a209-19d5dd2b7f3e": "3月，4月",
	"8828d614-3e9b-4c91-b96d-ab99123904c1;e1a715db-fb19-40eb-aba6-20a388a8138a;7eea46b5-deeb-4d70-a344-bbf15d68caee": "9月，10月",
	"ac2b2104-3858-4ef1-ba09-40fbcc995882;8828d614-3e9b-4c91-b96d-ab99123904c1": "7月，8月",
	"bd1eb50f-6b5e-4d13-a0d9-9c357a5c3812;02be87fe-14b9-4efc-a3ad-f63c7903735a;42efe3b5-f212-43f8-a027-5ba9044c1cd8": "5月，6月",
	"c4c82848-750b-4ee8-a4e1-c6d049d81c53;525fb15a-8641-4e7c-9b6f-e3400e9e14f3": "11月，12月",
	"cdd0f14d-9574-474a-b04b-a728066e910f;566526c1-8854-4afa-a702-cafdadb6acc1": "1月，2月",
	"e1a715db-fb19-40eb-aba6-20a388a8138a;7eea46b5-deeb-4d70-a344-bbf15d68caee;c4c82848-750b-4ee8-a4e1-c6d049d81c53": "9月，10月，11月",
	"e1a715db-fb19-40eb-aba6-20a388a8138a;7eea46b5-deeb-4d70-a344-bbf15d68caee": "9月，10月",
	"5a5f859d-ab26-4eac-8be2-55031227dd0b": "上半年",
	"6c897fec-ea07-460e-b5d6-3b34940613b8": "一季度",
	"cdd0f14d-9574-474a-b04b-a728066e910f": "1月",
	"566526c1-8854-4afa-a702-cafdadb6acc1": "2月",
	"881a4f82-de58-4f9e-ac0a-df4002a22273": "3月",
	"c3dfa1e6-c239-4eec-bb19-65a29c5dfe75": "二季度",
	"6b1515c0-57ec-4a16-a209-19d5dd2b7f3e": "4月",
	"02be87fe-14b9-4efc-a3ad-f63c7903735a": "5月",
	"42efe3b5-f212-43f8-a027-5ba9044c1cd8": "6月",
	"bd1eb50f-6b5e-4d13-a0d9-9c357a5c3812": "下半年",
	"a74a8b38-62e8-48a9-8ed7-72b9c7eefd6d": "三季度",
	"ac2b2104-3858-4ef1-ba09-40fbcc995882": "7月",
	"8828d614-3e9b-4c91-b96d-ab99123904c1": "8月",
	"e1a715db-fb19-40eb-aba6-20a388a8138a": "9月",
	"a9135cd7-aaf8-436e-a2e9-3e8dc52b53de": "四季度",
	"7eea46b5-deeb-4d70-a344-bbf15d68caee": "10月",
	"c4c82848-750b-4ee8-a4e1-c6d049d81c53": "11月",
	"525fb15a-8641-4e7c-9b6f-e3400e9e14f3": "12月",
	"dacaf6d9-829e-4a7e-87ee-e00700743313": "年度",

}

zuoye = {
	"1": "演练测试",
	"2": "漏洞扫描",
	"3": "安全预警",
	"4": "密码修改",
	"5": "补丁更新",
	"6": "权限检查",
	"7": "阀值评估",
	"8": "备份检查",
	"9": "其它工作",
	"10": "员工入离职",
}

reporttyppe = {
	"1": "运维报告",
	"2": "扫描报告",
	"3": "防病毒报告",
	"4": "专项报告",
	"5": "巡检报告",
	"6": "压力测试报告",
}

status = {
	"1": "待处理",
	"2": "处理中",
	"3": "已完成",
	"7": "已关闭",
	"10": "挂起",
	"11": "已废除",
}

urgentLevel = {
	"0": "",
	"1": "极低",
	"2": "低",
	"3": "中",
	"4": "高",
	"5": "极高"
}

chgResult = {
	"0":"成功",
	"1":"失败",
}

model_dict = {
	# "INTERNET_ACCESS_APPLY": "b74f82d248014f84b291e15afb2c361a", #外网接入申请
	# "NET_WORK_RESOURCE_REQUEST": "6064414adfab43788663cf2c980c855f",  #联网资源申请
	# "BUSINESS_SYSTEM_REQUEST": "0a540d16e49941319605ab73e53fdead",  #业务系统申请
	# "RESOURCE_ASSET_MANAGEMENT_PROCESS": "f7f3bfc2fdcc427594c5ce9f5cff43d4",  #资源资产管理流程
	# "DEVICE_PUTAWAY_PROCESS": "0b2371f8142b4a48bb623d9d9c5344ce",  # 设备上架流程
	# "DEVICE_UNSHELVE_PROCESS": "b8ebe358d4cf4fddb53ce8f15814b73e",  #设备下架流程
	# "ASSET_RETIREMENT_PROCESS": "74b927f245d248d29693aac41002684c",  #资产报废流程
	# "ASSET_STORAGE_PROCESS": "a2141adb0a0e4bb4898f5440896442be",  #资产入库流程
	# "ASTBG": "40b1fa503b7d409293351f26b18caa51",  #资产变更流程

	# "OFFICE_INTERNET_ANTIVIRUS_REPORT": "fabbc05278c94aaf8646e30878531b9d",  #办公互联网防病毒报告
	# "TASK_MANAGE_SUBPROCESS": "7f37a0f702224e19a8b4fb4c56cd51cb",  #任务管理管理子流程
	# "REPORT_CHANNEL": "14d9536e16cf401b8cda13970ca6e8ff",  #报告专用通道
	# "LDSM": "559cbed4ec95463992237e40618111d5",  # 漏洞扫描与渗透测试申请

	# "BUSINESS_SYSTEM_ONLINE": "c0681b0118524011aaa201a1a497254f",  #业务系统上线申请
	# "EVENT_ORDER": "e05344e580e947fca2ffe57863dd02ce", #事件工单
	# "CHANGE_AND_RELEASE": "345ba0b64f054df08f2f1f051cd5c2ed",  #变更与发布
	# "CLOUD_RESOURCE_VMS_ADJUST": "08522a2c7dbe4430895ceecc33f4051b",  #云资源虚拟机调整销毁
	# "CLOUD_RESOURCE_VM_APPLY_2022": "78a3bfc8bcfa4a8c834ddd04f547ee8a",  #云资源虚拟机申请2022
	# "TASK_MANAGEMENT_PROCESS": "98786a83568446389e4d7ee79b2e71bb",  #任务管理
	# "NOTIFICAITON_ANNOUNCEMENT": "155efe444fff49ee924bcb0d2995d068",  #通知公告
	# "VPN_RESOURCE_APPLY": "eb3605913e264c5faa9e44cdc002ed24",  #VPN资源申请
	# "BASTION_HOST_APPROVE": "7c7b3946421e4cd980e086d6d7b76d4f",  #堡垒机审批
	# "RISK": "7658c4feee12465d8f90afd3a5bdd701",  #网络与信息安全漏洞风险处理流程
	# "SELF": "7f9509cd02b84616a7cdd9c7e9d4a7c6",  #运维平台本身BUG或需求提交
	# "SALES_LOG": "4e9afa26247447a8915fdef991abe56b",  #营业部日志
	# "TW": "2ecd27293852479e943b5f9583de9f01",  #特维报告管理
	# "APPLY_EXTRA_WORK": "400ca74adba247bd9097e4c0cb52a21d",  #加班申请
	# "MJ": "9de8a7ef46ff4f7593cbaa3e99fbf95e",  #门禁申请
	# "FBIP": "6e8a160e123c4a3786e05842bb17b799",  #封闭IP申请
	# "XMZS": "350fa3ef75fb47ea96c9ca324089e4b5",  #项目助手
	# "PROBLEM": "1cfa73b5acb34603a32487cffb9f0559",  #问题工单
	# "JIEJEUFA": "157d9e362b554ef4903901d695a1dc02",  #测试流程
	# "YDGZZJTZ": "22042f9e6c4d42368b65a3c8bbdde3b4",  #月度工作总结提交通知
	"JFAZKNSL": "bec4923a532a450e811be9f902c2310c",  #供应商准入审核2024
	# "SJXQGL": "b875292208cb42a2aba2583084c9e18f",  #数据需求管理
	# "XXBWQB": "b1480d8d94484f41b26a706000574d69",  #信息部外勤表
	# "BXX": "ba5189b481fb41e3b88067adc7771d79",  #业务系统停用及下线流程
	# "SORTLOOM": "55e8646d5734405783b3e7cc85171d60",  #补丁测试
}

if __name__ == "__main__":
	environ_judge = environ_judge()
	uv_host = "***********"

	# 执行模型字典中定义的模块
	for model,model_id in model_dict.items():
		# 各阶段成功、失败数统计
		executive_result = {
			"mongo_result_count": 0,
			"post_data_count": 0,
			"getAttachFile_failure": 0,
			"getAttachFile_other_failure": 0,
			"FILES_data": 0,
			"replace_dict_value_fairure": 0,
			"mongo_input_uv_creator_failure": 0,
			"mongo_input_uv_failure": 0,
			"Attachment_data_deal": 0,
			"post_data_failure": 0,
			"getProcessRecord_failure": 0,
			"upload_file_failure": 0
		}

		# 获取附件字典
		file_prefix = model
		FILES_list = "%s_FILES" %(model)
		FILES_list = globals()[FILES_list] if FILES_list in globals() else FILES_list

		# 根据操作系统指定不同的路径参数
		environ_judge.my_environ_judge(file_prefix)
		file_path = environ_judge.file_path
		log_file = environ_judge.log_file

		# 获取对照表：模型id、创建者、执行者、申请人
		modelId = get_modelId(file_path)
		creator = get_userinfo(file_path)
		executor = creator
		applicant = creator
		intreport = creator

		# 获取mongo数据及mysql数据
		mongo_results = mongo_find(table="hnt_tk_TicketObject",id = {"modelId": model_id})
		mongo_LinkedAttach = mongo_find(table='hnt_tk_TicketLinkedAttach',id={})
		mysql_result = mysql_find()

		#
		main_process(mongo_results)

		print u"- MODEL: %s  -  执行mongo数据条数：%s  -  mongo_input_uv 失败：%s  -  数据上传成功：%s" %(model,executive_result["mongo_result_count"], executive_result["mongo_input_uv_failure"], executive_result["post_data_count"])
		print u"- replace_dict_value_fairure：%s  -  getProcessRecord_failure: %s" %(executive_result["replace_dict_value_fairure"],executive_result["getProcessRecord_failure"])
#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-
import sys
import json
import requests
from datetime import datetime

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

# CMDB 服务器主机名
HOST = EASYOPS_CMDB_HOST.split(":")[0]

# 通知方式的枚举映射
method_enums = {
    "site_msg": "站内信",
    "email": "邮件",
    "dingding": "钉钉",
    "dingding_robot": "钉钉机器人",
    "wework": "企业微信",
    "message": "短信"
}

def convert_time(time_str, adjust_minutes=0, adjust_seconds=0):
    """
    将时间字符串转换为标准格式
    :param time_str: 输入的时间字符串 (ISO 8601 格式，如 "2025-03-25T14:30:00Z")
    :param adjust_minutes: 调整的分钟数（未使用）
    :param adjust_seconds: 调整的秒数（未使用）
    :return: 格式化后的时间字符串 "YYYY-MM-DD HH:MM:SS"
    """
    dt = datetime.strptime(time_str[:19], "%Y-%m-%dT%H:%M:%S")  # 解析时间
    formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")  # 格式化时间
    return formatted_time


def parser_formdata(formdata):
    """
    解析表单数据，提取公告相关信息，并调用 set_notification() 发送通知
    :param formdata: JSON 格式的表单数据
    """
    for i in json.loads(formdata):  # 解析 JSON 格式的表单数据
        print(json.dumps(i))  # 打印解析后的数据，方便调试
        if i["key"] == "h4hevi8tax":  # 过滤出特定的表单项
            annex_data = []  # 存储附件信息的列表
            for value in i["values"]:
                # 获取公告信息字段
                annex = value.get("annex")  # 附件信息
                posting_title = value.get("posting_title")  # 公告标题
                failure_time = value.get("failure_time")  # 过期时间
                contents = value.get("contents")  # 公告内容
        
        # 解析附件信息
        for i in annex:
            checksum = i["checksum"]  # 附件校验码
            size = i["size"]  # 附件大小
            source = i["source"]  # 附件来源
            fileName = i["fileName"]  # 附件名称
            annex_data.append({
                "instanceId": "",
                "checksum": checksum,
                "size": size,
                "source": source,
                "name": fileName
            })

    # 构造公告通知数据
    notification_data = {
        "notifyConf": {
            "method": ["site_msg"],  # 发送方式（目前仅支持站内信）
            "receivers": []  # 接收者（目前为空）
        },
        "category": "other",  # 公告类别
        "title": posting_title,  # 公告标题
        "expires": convert_time(failure_time),  # 过期时间（格式化）
        "fileInfo": annex_data,  # 附件信息
        "data": contents,  # 公告正文内容
        "isPublish": True  # 是否发布公告
    }
    
    set_notification(notification_data)  # 发送公告


def set_notification(notification_data):
    """
    发送公告通知
    :param notification_data: 需要发送的公告数据
    """
    url = "http://{}:8271/api/sys_setting/v1/announce".format(HOST)  # API 地址
    headers = {
        'org': str(EASYOPS_ORG),
        'user': 'easyops'
    }
    response = requests.post(url=url, headers=headers, data=json.dumps(notification_data)).text
    print(response)  # 打印服务器返回的信息


if __name__ == "__main__":
    """
    主程序入口：
    1. 解析 `orderInfo`，提取 `stepList` 数据
    2. 过滤出 `userTaskId` 为 "Activity_1wichp2" 的步骤
    3. 解析该步骤的 `formData`，并进行公告发送
    """
    orderInfo = json.loads(orderInfo)  # 解析订单信息
    stepList = orderInfo["stepList"]  # 获取步骤列表

    for i in stepList:
        if i.get("userTaskId") == "Activity_1wichp2":  # 只处理特定的任务步骤
            formdata = i.get("formData")  # 提取表单数据
            parser_formdata(formdata)  # 解析表单并发送公告
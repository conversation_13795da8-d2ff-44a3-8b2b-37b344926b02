{"isSubStep": false, "operatorLeader": "", "userTaskList": [{"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "1", "handling": "directly", "name": "发起申请", "labelViews": [], "formExpressionName": "os_type:Activity_0xuq1we.h5k3buccoh.0.h5k3buccol.value", "subsequentConf": [], "standardFields": [], "operationConf": [], "jumpableNodes": [], "assigneeGroups": [], "formVersionId": "6349a475104fe", "formDisplayMode": "side", "fbForm": null, "isNextPar": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "", "value": "os_type == \"1\""}, "targetTaskId": "Activity_19sgyi6", "name": "跳转至windows管理员审批"}, {"isSubProcess": false, "conditionExpression": {"name": "", "value": "os_type == \"2\""}, "targetTaskId": "Activity_0zr93wj", "name": "跳转至linux管理员审批"}], "formDefinition": "[{\"key\":\"h5k3buccoh\",\"name\":\"基本信息\",\"condition\":true,\"layout\":[0,0,12,1],\"type\":\"row\",\"displayCondition\":\"\",\"propertys\":[{\"key\":\"h5k3buccoi\",\"type\":\"INPUT\",\"label\":\"主机名\",\"modelField\":\"h5k3buccoi\",\"options\":{\"extraProps\":{},\"layout\":[0,0,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"isEnablePattern\":false},\"belongToSection\":\"h5k3buccoh\"},{\"key\":\"h5k3buccok\",\"type\":\"INPUT\",\"label\":\"IP地址\",\"modelField\":\"h5k3buccok\",\"options\":{\"extraProps\":{},\"layout\":[0,1,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"isEnablePattern\":false},\"belongToSection\":\"h5k3buccoh\"},{\"key\":\"h5k3buccol\",\"type\":\"SELECT\",\"label\":\"操作系统\",\"modelField\":\"h5k3buccol\",\"options\":{\"extraProps\":{\"items\":[{\"key\":\"option-15\",\"label\":\"windows\",\"value\":\"1\"},{\"key\":\"option-16\",\"label\":\"linux\",\"value\":\"2\"}]},\"layout\":[0,2,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"object\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"dataIndex\":\"label\"},\"belongToSection\":\"h5k3buccoh\"},{\"key\":\"h5k3buccom\",\"type\":\"CMDBINSTANCESELECT\",\"label\":\"负责人\",\"modelField\":\"h5k3buccom\",\"options\":{\"extraProps\":{\"objectId\":\"USER\",\"url\":\"\",\"user\":\"\",\"listMode\":false},\"layout\":[0,3,12,1],\"labelCol\":3,\"defaultValue\":[],\"required\":true,\"dataType\":\"objectarray\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"primaryKey\":[\"instanceId\"],\"frontKey\":[\"name\"]},\"belongToSection\":\"h5k3buccoh\"},{\"key\":\"h5k3buccon\",\"type\":\"CMDBCASCADER\",\"label\":\"应用\",\"modelField\":\"h5k3buccon\",\"options\":{\"extraProps\":{\"objectIdPath\":[{\"objectId\":\"BUSINESS\",\"showKey\":[\"name\"]},{\"objectId\":\"APP\",\"showKey\":[\"name\"]}]},\"layout\":[0,4,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"frontKey\":\"name\",\"primaryKey\":\"instanceId\"},\"belongToSection\":\"h5k3buccoh\"},{\"key\":\"h60k6xk1is\",\"type\":\"RICHTEXT\",\"label\":\"linux配置信息\",\"modelField\":\"h60k6xk1is\",\"options\":{\"extraProps\":{\"showOperationButton\":true},\"layout\":[0,5,12,4],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"xml\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"#{h5k3buccoh.h5k3buccol}.value == \\\"2\\\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[]},\"belongToSection\":\"h5k3buccoh\"},{\"key\":\"h60ln9hl4x\",\"type\":\"CHECKBOX\",\"label\":\"包容网关测试\",\"modelField\":\"h60ln9hl4x\",\"options\":{\"extraProps\":{\"items\":[{\"key\":\"option-227\",\"label\":\"a\",\"value\":\"1\"},{\"key\":\"option-228\",\"label\":\"b\",\"value\":\"2\"},{\"key\":\"option-229\",\"label\":\"c\",\"value\":\"3\"}]},\"layout\":[0,9,12,1],\"labelCol\":3,\"defaultValue\":[],\"required\":true,\"dataType\":\"objectarray\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"dataIndex\":\"label\"},\"belongToSection\":\"h5k3buccoh\"},{\"key\":\"h75bvr88nt\",\"type\":\"UPLOAD\",\"label\":\"附件\",\"modelField\":\"h75bvr88nt\",\"options\":{\"extraProps\":{\"buttonText\":\"附件上传\",\"max_number\":5,\"max_size\":128,\"isEnableFileTypeLimit\":false,\"fileType\":\"\"},\"layout\":[0,10,12,1],\"labelCol\":3,\"defaultValue\":[],\"required\":true,\"dataType\":\"filearray\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"dataIndex\":\"fileName\"},\"belongToSection\":\"h5k3buccoh\"}]},{\"key\":\"h60k6xk1ip\",\"name\":\"windows配置信息\",\"condition\":true,\"layout\":[0,6,12,1],\"type\":\"row\",\"displayCondition\":\"#{h5k3buccoh.h5k3buccol}.value == \\\"1\\\"\",\"propertys\":[{\"key\":\"h60k6xk1iq\",\"type\":\"INPUT\",\"label\":\"字段标题\",\"modelField\":\"h60k6xk1iq\",\"options\":{\"extraProps\":{},\"layout\":[0,0,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"isEnablePattern\":false},\"belongToSection\":\"h60k6xk1ip\"},{\"key\":\"h60k6xk1ir\",\"type\":\"INPUT\",\"label\":\"字段标题\",\"modelField\":\"h60k6xk1ir\",\"options\":{\"extraProps\":{},\"layout\":[0,1,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"isEnablePattern\":false},\"belongToSection\":\"h60k6xk1ip\"}]},{\"key\":\"h5k3buccoo\",\"name\":\"表格\",\"condition\":true,\"layout\":[0,8,12,1],\"type\":\"table\",\"displayCondition\":\"\",\"propertys\":[{\"key\":\"h5k3buccop\",\"type\":\"INPUT\",\"label\":\"主机名\",\"modelField\":\"h5k3buccop\",\"options\":{\"extraProps\":{},\"layout\":[0,0,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"isEnablePattern\":false},\"belongToSection\":\"h5k3buccoo\"},{\"key\":\"h5k3buccoq\",\"type\":\"TEXTAREA\",\"label\":\"描述\",\"modelField\":\"h5k3buccoq\",\"options\":{\"extraProps\":{},\"layout\":[0,1,12,2],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[]},\"belongToSection\":\"h5k3buccoo\"},{\"key\":\"h5k3buccor\",\"type\":\"SELECT\",\"label\":\"测试使用\",\"modelField\":\"h5k3buccor\",\"options\":{\"extraProps\":{\"items\":[{\"key\":\"option-21\",\"label\":\"a\",\"value\":\"1\"},{\"key\":\"option-22\",\"label\":\"b\",\"value\":\"2\"}]},\"layout\":[0,3,12,1],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"object\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"dataIndex\":\"label\"},\"belongToSection\":\"h5k3buccoo\"}],\"extraProps\":{\"operationsSettings\":[{\"value\":\"addControl\",\"label\":\"新增数据\"},{\"value\":\"modifyControl\",\"label\":\"修改数据\"},{\"value\":\"deleteControl\",\"label\":\"删除数据\"},{\"value\":\"exportControl\",\"label\":\"导出模板\"},{\"value\":\"importControl\",\"label\":\"导入模板\"}]}},{\"key\":\"h5kupa8hmh\",\"name\":\"标签页容器\",\"condition\":true,\"layout\":[0,13,12,1],\"type\":\"tabs\",\"displayCondition\":\"\",\"propertys\":[],\"tabPanes\":[{\"key\":\"h5kuthtep4\",\"tab\":\"页签1\",\"propertys\":[{\"key\":\"h5kupa8hmi\",\"type\":\"RICHTEXT\",\"label\":\"输入内容\",\"modelField\":\"h5kupa8hmi\",\"options\":{\"extraProps\":{\"showOperationButton\":true},\"layout\":[0,0,12,4],\"labelCol\":3,\"defaultValue\":\"\",\"required\":true,\"dataType\":\"xml\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[]},\"belongToSection\":\"h5kupa8hmh\"}],\"displayCondition\":\"\",\"displayUserTaskId\":\"\"},{\"key\":\"h5kutkll6g\",\"tab\":\"页签2\",\"propertys\":[{\"key\":\"h5kupa8hmj\",\"type\":\"UPLOAD\",\"label\":\"上传附件\",\"modelField\":\"h5kupa8hmj\",\"options\":{\"extraProps\":{\"buttonText\":\"Click Upload\",\"max_number\":5,\"max_size\":128,\"isEnableFileTypeLimit\":false},\"layout\":[0,0,12,1],\"labelCol\":3,\"defaultValue\":[],\"required\":true,\"dataType\":\"filearray\",\"pattern\":\"\",\"placeholder\":\"\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"question\":[],\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"dataIndex\":\"fileName\"},\"belongToSection\":\"h5kupa8hmh\"}],\"displayCondition\":\"\",\"displayUserTaskId\":\"\"}]},{\"key\":\"h5kupa8hmk\",\"name\":\"cmdb实例操作性容器\",\"condition\":true,\"layout\":[0,18.8,12,1],\"type\":\"cmdb_instance_operate_container\",\"displayCondition\":\"\",\"propertys\":[{\"key\":\"businessCmdbInstance\",\"type\":\"INPUT\",\"label\":\"变更实例\",\"modelField\":\"businessCmdbInstance\",\"options\":{\"extraProps\":{},\"layout\":[0,0,12,1],\"labelCol\":4,\"defaultValue\":\"\",\"required\":false,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"请输入变更实例\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"fieldInfo\":{\"key\":\"businessCmdbInstance\",\"label\":\"变更实例\",\"value\":{}},\"isEnablePattern\":false},\"belongToSection\":\"h5kupa8hmk\"},{\"key\":\"resourceInstanceId\",\"type\":\"INPUT\",\"label\":\"资源实例ID\",\"modelField\":\"resourceInstanceId\",\"options\":{\"extraProps\":{},\"layout\":[0,0,12,1],\"labelCol\":4,\"defaultValue\":\"\",\"required\":false,\"dataType\":\"string\",\"pattern\":\"\",\"placeholder\":\"请输入资源实例ID\",\"disabled\":false,\"enabled\":true,\"highLight\":false,\"isMore\":true,\"only\":false,\"note\":\"\",\"displayCondition\":\"\",\"remoteFunc\":{\"toolId\":\"\",\"scriptInputs\":[]},\"rules\":[],\"fieldInfo\":{\"id\":\"resourceInstanceId\",\"name\":\"资源实例ID\",\"key\":\"resourceInstanceId\",\"label\":\"资源实例ID\",\"required\":\"false\",\"value\":{\"type\":\"str\",\"struct_define\":[]}},\"isEnablePattern\":false},\"belongToSection\":\"h5kupa8hmk\"}],\"extraProps\":{\"pattern\":[{\"key\":\"add\",\"value\":[\"addControl\",\"modifyControl\",\"deleteControl\",\"exportControl\"]},{\"key\":\"edit\",\"value\":[\"modifyControl\",\"deleteControl\",\"exportControl\"]},{\"key\":\"delete\",\"value\":[\"deleteControl\"]}],\"cmdbInstanceChangeModel\":{\"objectId\":\"HOST\",\"cmdbModifyFields\":{\"writeContainerAttrList\":[{\"type\":\"INPUT\",\"label\":\"资源实例ID\",\"key\":\"\",\"modelField\":\"resourceInstanceId\",\"options\":{\"layout\":[0,0,12,1],\"labelCol\":2,\"dataIndex\":\"\",\"defaultValue\":\"\",\"dataType\":\"string\",\"required\":\"false\",\"disabled\":false,\"only\":\"false\",\"pattern\":null},\"belongToSection\":\"h5kupa8hmk\",\"cmdbProps\":{\"regex\":null,\"class\":\"attr\",\"type\":\"str\"}}],\"attrIds\":[\"resourceInstanceId\"],\"relationAttrIds\":[]}}},\"options\":{\"frontKey\":[\"instanceId\"]}}]", "type": "assignee", "id": "Activity_0xuq1we", "formName": "主机申请流程测试0317-发起申请"}, {"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "0", "handling": "directly", "name": "windows管理员审批", "labelViews": [], "formExpressionName": "", "subsequentConf": [], "standardFields": [], "operationConf": [], "jumpableNodes": [], "assigneeGroups": [], "formVersionId": "", "formDisplayMode": "side", "fbForm": null, "isNextPar": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Activity_0w88z0a", "name": "跳转至实施"}], "formDefinition": "", "type": "", "id": "Activity_19sgyi6", "formName": ""}, {"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "0", "handling": "directly", "name": "linux管理员审批", "labelViews": [], "formExpressionName": "", "subsequentConf": [], "standardFields": [], "operationConf": [], "jumpableNodes": [], "assigneeGroups": [], "formVersionId": "", "formDisplayMode": "side", "fbForm": null, "isNextPar": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Activity_0w88z0a", "name": "跳转至实施"}], "formDefinition": "", "type": "assignee", "id": "Activity_0zr93wj", "formName": ""}, {"assigneeListUser": [], "isFormDecision": "0", "handling": "directly", "name": "实施", "labelViews": [], "formExpressionName": "", "subsequentConf": [], "standardFields": [], "operationConf": [], "jumpableNodes": [], "assigneeGroups": [], "formVersionId": "", "formDisplayMode": "side", "fbForm": null, "isNextPar": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Activity_1giay94", "name": "跳转至申请人验收"}], "formDefinition": "", "type": "assignee", "id": "Activity_0w88z0a", "formName": ""}, {"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "0", "handling": "directly", "name": "申请人验收", "labelViews": [], "formExpressionName": "", "subsequentConf": [], "standardFields": [], "operationConf": [], "jumpableNodes": [], "assigneeGroups": [], "formVersionId": "", "formDisplayMode": "side", "fbForm": null, "isNextPar": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Event_1asihg4", "name": "跳转至结束"}], "formDefinition": "", "type": "assignee", "id": "Activity_1giay94", "formName": ""}], "creator": "<PERSON><PERSON><PERSON>", "instanceId": "63577351f3426", "variables": [], "executionId": "", "unassigned": false, "isAck": false, "mtime": "2025-05-19 14:26:19", "operator": "<PERSON><PERSON><PERSON>", "creatorShowName": "", "subProcessInstanceId": "", "flowableTaskId": "", "serviceInstance": {"ownerGroupList": [], "category": "服务请求", "name": "主机申请测试", "instanceId": "631b13da8f11f", "ownerList": ["easyops"], "priority": null, "slaEnabled": false, "owner": "easyops"}, "otime": "2025-05-19 14:26:19", "extraAssigneeType": "", "isDelete": false, "processInstance": {"rTime": "", "suspendInfo": null, "creator": "<PERSON><PERSON><PERSON>", "instanceId": "635770817cbbe", "isSubInstance": false, "ctime": "2025-05-19 14:13:33", "isCancelled": false, "creatorShowName": "", "isDelete": false, "supervisorList": [], "operationTime": "2025-05-19 14:26:19", "category": "服务请求", "influenceScope": "", "lastDiscussTime": "", "timeoutTime": "", "isSuspended": false, "etime": "", "orderNum": "REQ25051900011", "source": "ITSC", "suspendTimeLimit": 0, "versionRelevanceSubTaskInfo": [], "subsequentConf": [], "status": "running", "suspendCost": 0, "handleWay": "common", "stepIdList": [], "isOldProcessInstance": false, "flowableInstanceId": "6460a31f-3478-11f0-97b2-525400114ec9", "serviceId": "631b13da8f11f", "isComment": false, "versionRelevanceUserTaskInfo": [{"isDesensitization": false, "fbFormInstanceId": "", "formVersionId": "6349a475104fe", "fbFormId": "", "formDisplayMode": "side", "userTaskId": "Activity_0xuq1we"}], "name": "苗锋_主机申请测试_20250519141153", "oldProcessInstanceStruct": {"category": "", "ctime": "", "creator": "", "pid": "", "oldVersionRelevanceUserTaskInfo": "", "pname": "", "id": 0}, "suspendTimeLimitConf": [], "isTimeout": false, "currentAssigneeList": [], "slaStatus": "", "visibleRange": "operator", "focusFields": [], "urgency": ""}, "stepOperationRecord": [{"extraAssigneeType": "", "stepId": "63577081bd42b", "toUserGroup": [], "processInstanceId": "635770817cbbe", "memo": "", "isExtraAssignee": false, "comments": [], "subProcessInstanceId": "", "isSubProcess": false, "toUser": [], "action": "done", "taskName": "发起申请", "operator": {"username": "<PERSON><PERSON><PERSON>", "instanceId": "632ef1363d3ef", "state": "", "userIcon": "", "nickname": "苗锋", "showName": "<PERSON><PERSON><PERSON>(苗锋)"}, "recordCtime": "2025-05-19 14:13:36", "userTaskId": "Activity_0xuq1we", "formData": "", "operationTime": "2025-05-19 14:13:36", "operationId": "682acc109ca9f7e778404314"}, {"extraAssigneeType": "", "stepId": "6357708419f2a", "toUserGroup": [], "processInstanceId": "635770817cbbe", "memo": "同意", "isExtraAssignee": false, "comments": [], "subProcessInstanceId": "", "isSubProcess": false, "toUser": [], "action": "done", "taskName": "linux管理员审批", "operator": {"username": "<PERSON><PERSON><PERSON>", "instanceId": "632ef1363d3ef", "state": "", "userIcon": "", "nickname": "苗锋", "showName": "<PERSON><PERSON><PERSON>(苗锋)"}, "recordCtime": "2025-05-19 14:26:08", "userTaskId": "Activity_0zr93wj", "formData": "", "operationTime": "2025-05-19 14:26:08", "operationId": "682acf00c5d479cde7fd50c5"}, {"extraAssigneeType": "", "stepId": "63577351f3426", "toUserGroup": [], "processInstanceId": "635770817cbbe", "memo": "同意", "isExtraAssignee": false, "comments": [], "subProcessInstanceId": "", "isSubProcess": false, "toUser": [], "action": "done", "taskName": "实施", "operator": {"username": "<PERSON><PERSON><PERSON>", "instanceId": "632ef1363d3ef", "state": "", "userIcon": "", "nickname": "苗锋", "showName": "<PERSON><PERSON><PERSON>(苗锋)"}, "recordCtime": "2025-05-19 14:26:19", "userTaskId": "Activity_0w88z0a", "formData": "", "operationTime": "2025-05-19 14:26:19", "operationId": "682acf0bc5d479cde7fd50c6"}], "formData": "[]", "finishedStepList": ["Activity_0xuq1we", "Activity_0zr93wj", "Activity_0w88z0a"], "etime": "2025-05-19 14:26:19", "process": {"instanceId": "630ad7a682335", "category": "", "name": "主机申请流程测试"}, "userTaskInfo": [{"status": "running", "instanceId": "6357735da7416", "assigneeGroup": [], "assignee": ["<PERSON><PERSON><PERSON>"], "role": "assignee", "taskName": "申请人验收", "type": "assignee", "assigneeDepts": [], "userTaskId": "Activity_1giay94"}], "serviceRelevanceOrder": [], "userInfoMap": {"miaofeng": "苗锋", "easyops": "系统管理员"}, "rTime": "", "taskName": "实施", "type": "", "status": "done", "allowedOp": {"canSuspend": true, "canClaim": false, "canWithdraw": false, "canAssignee": false, "canCc": false, "canDone": false, "canAddExtraAssignee": false, "canDistribute": false, "canComment": false, "canSLAChange": true, "canConvert": false, "canClose": false, "canNextAssignee": false, "canRevoke": false}, "subTaskList": [], "stopAts": ["Activity_1giay94"], "memo": "", "isExtraAssignee": false, "operatorShowName": "", "stepList": [{"status": "running", "otime": "", "formData": "", "ctime": "2025-05-19 14:26:21", "toolStatus": "", "isSubStep": false, "instanceId": "6357735da7416", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "", "assignees": {"assigneeGroupList": [], "role": "assignee", "assigneeList": ["<PERSON><PERSON><PERSON>"]}, "action": "", "mtime": "", "operator": "", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "申请人验收", "consignors": [], "userTaskId": "Activity_1giay94"}, {"status": "done", "otime": "2025-05-19 14:13:35", "formData": "[{\"key\":\"h5k3buccoh\",\"values\":[{\"h5k3buccoi\":\"测试********\",\"h5k3buccok\":\"127.1.1.2\",\"h5k3buccol\":{\"value\":\"2\",\"label\":\"linux\",\"key\":\"2\"},\"h5k3buccom\":[{\"_impl_from\":[],\"_object_id\":\"USER\",\"_object_version\":20,\"_pre_ts\":**********,\"_ts\":**********,\"_version\":5,\"creator\":\"chenzhigang\",\"ctime\":\"2025-04-17 09:11:04\",\"deleteAuthorizers\":[],\"easyops_account_type\":\"normal\",\"instanceId\":\"632ef1363d3ef\",\"modifier\":\"miaofeng\",\"mtime\":\"2025-04-30 10:33:46\",\"name\":\"miao<PERSON>\",\"nickname\":\"苗锋\",\"org\":**********,\"readAuthorizers\":[],\"state\":\"valid\",\"updateAuthorizers\":[],\"user_email\":\"<EMAIL>\"}],\"h5k3buccon\":[{\"instanceId\":\"63086b52d71d1\",\"name\":\"business1\"},{\"instanceId\":\"63086bcccb3b3\",\"name\":\"app1\"}],\"h60ln9hl4x\":[{\"key\":\"option-227\",\"label\":\"a\",\"value\":\"1\"}],\"h75bvr88nt\":[{\"checksum\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"fileName\":\"文件上传测试.txt\",\"size\":0,\"instanceId\":\"\",\"source\":\"oss\"}],\"h60k6xk1is\":\"<p>1c</p>\"}]},{\"key\":\"h5k3buccoo\",\"values\":[{\"h5k3buccop\":\"测试********\",\"h5k3buccoq\":\"测试********\",\"h5k3buccor\":{\"value\":\"1\",\"label\":\"a\",\"key\":\"1\"},\"itsc_uuid_\":\"h7hgiqhdf4\"}]},{\"key\":\"h5kupa8hmh\",\"values\":[{\"h5kupa8hmi\":\"<p>1</p>\"},{\"h5kupa8hmj\":[{\"checksum\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"fileName\":\"文件上传测试.txt\",\"size\":0,\"instanceId\":\"\",\"source\":\"oss\"}]}]},{\"key\":\"h5kupa8hmk\",\"values\":[]}]", "ctime": "2025-05-19 14:13:33", "toolStatus": "", "isSubStep": false, "instanceId": "63577081bd42b", "subProcessInstanceStepId": "", "memo": "", "isExtraAssignee": false, "etime": "2025-05-19 14:13:35", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "发起申请", "consignors": [], "userTaskId": "Activity_0xuq1we"}, {"status": "done", "otime": "2025-05-19 14:26:08", "formData": "[]", "ctime": "2025-05-19 14:13:36", "toolStatus": "", "isSubStep": false, "instanceId": "6357708419f2a", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-19 14:26:08", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "linux管理员审批", "consignors": [], "userTaskId": "Activity_0zr93wj"}, {"status": "done", "otime": "2025-05-19 14:26:19", "formData": "[]", "ctime": "2025-05-19 14:26:08", "toolStatus": "", "isSubStep": false, "instanceId": "63577351f3426", "subProcessInstanceStepId": "", "memo": "同意", "isExtraAssignee": false, "etime": "2025-05-19 14:26:19", "assignees": null, "action": "", "mtime": "", "operator": "<PERSON><PERSON><PERSON>", "fileInfo": "", "subProcessInstanceId": "", "type": "assignee", "taskName": "实施", "consignors": [], "userTaskId": "Activity_0w88z0a"}], "consignors": [], "extraAssigneeList": [], "timeoutTime": "", "nodeList": [{"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "1", "handling": "directly", "name": "发起申请", "links": {"outgoing": ["Flow_0aa5jdl"], "incoming": ["Flow_1qmhbru"]}, "isNextPar": false, "skipStragety": "emptyAssign", "formExpressionName": "os_type:Activity_0xuq1we.h5k3buccoh.0.h5k3buccol.value", "assigneeGroups": [], "userType": "specifyUser", "countersignRate": 0, "Setting": {"candidateSettings": [], "triggerIdList": ["5c9a95cd583c2"], "revokeRoles": ["admin"], "labelViews": [], "holidayHandleConf": {"enabled": false, "groupId": ""}, "userStrategyId": "", "cancelRoles": ["admin"], "memoLevel": 0, "scriptSettings": {"postScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}, "preScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}}, "nextAssigneeSetting": {"nextAssignees": [], "enabled": false}, "suspendSetting": {"isAutoActivate": false, "activateTime": 1}, "rejectNodes": [], "allowedOps": ["done", "SLAChange"], "userTaskId": "Activity_0xuq1we"}, "assigneeValue": "", "approveType": "single", "isFirst": true, "subProcess": {"subProcessId": "", "isSub": false}, "id": "Activity_0xuq1we", "isLast": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "", "value": "os_type == \"1\""}, "targetTaskId": "Activity_19sgyi6", "name": "跳转至windows管理员审批"}, {"isSubProcess": false, "conditionExpression": {"name": "", "value": "os_type == \"2\""}, "targetTaskId": "Activity_0zr93wj", "name": "跳转至linux管理员审批"}]}, {"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "0", "handling": "directly", "name": "windows管理员审批", "links": {"outgoing": ["Flow_12cpyu4"], "incoming": ["Flow_1rxnxwy"]}, "isNextPar": false, "skipStragety": "emptyAssign", "formExpressionName": "", "assigneeGroups": [], "userType": "specifyUser", "countersignRate": 0, "Setting": {"candidateSettings": [], "triggerIdList": ["5c9a95cd583c2"], "revokeRoles": ["admin"], "labelViews": [], "holidayHandleConf": {"enabled": false, "groupId": ""}, "userStrategyId": "", "cancelRoles": ["admin"], "memoLevel": 0, "scriptSettings": {"postScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}, "preScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}}, "nextAssigneeSetting": {"nextAssignees": [], "enabled": true}, "suspendSetting": {"isAutoActivate": false, "activateTime": 1}, "rejectNodes": [], "allowedOps": ["done", "SLAChange"], "userTaskId": "Activity_19sgyi6"}, "assigneeValue": "", "approveType": "single", "isFirst": false, "subProcess": {"subProcessId": "", "isSub": false}, "id": "Activity_19sgyi6", "isLast": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Activity_0w88z0a", "name": "跳转至实施"}]}, {"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "0", "handling": "directly", "name": "linux管理员审批", "links": {"outgoing": ["Flow_06szss7"], "incoming": ["Flow_1q9akd7"]}, "isNextPar": false, "skipStragety": "emptyAssign", "formExpressionName": "", "assigneeGroups": [], "userType": "specifyUser", "countersignRate": 0, "Setting": {"candidateSettings": [], "triggerIdList": ["5c9a95cd583c2"], "revokeRoles": ["admin"], "labelViews": [], "holidayHandleConf": {"enabled": false, "groupId": ""}, "userStrategyId": "", "cancelRoles": ["admin"], "memoLevel": 0, "scriptSettings": {"postScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}, "preScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}}, "nextAssigneeSetting": {"nextAssignees": [], "enabled": false}, "suspendSetting": {"isAutoActivate": false, "activateTime": 1}, "rejectNodes": [], "allowedOps": ["done", "SLAChange"], "userTaskId": "Activity_0zr93wj"}, "assigneeValue": "", "approveType": "single", "isFirst": false, "subProcess": {"subProcessId": "", "isSub": false}, "id": "Activity_0zr93wj", "isLast": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Activity_0w88z0a", "name": "跳转至实施"}]}, {"assigneeListUser": [], "isFormDecision": "0", "handling": "directly", "name": "实施", "links": {"outgoing": ["Flow_1coss7g"], "incoming": ["Flow_13mj9t4"]}, "isNextPar": false, "skipStragety": "emptyAssign", "formExpressionName": "", "assigneeGroups": [], "userType": "specifyUser", "countersignRate": 0, "Setting": {"candidateSettings": [], "triggerIdList": ["5c9a95cd583c2"], "revokeRoles": ["admin"], "labelViews": [], "holidayHandleConf": {"enabled": false, "groupId": ""}, "userStrategyId": "", "cancelRoles": ["admin"], "memoLevel": 0, "scriptSettings": {"postScript": {"operations": ["pass"], "scriptIdList": ["5089004f901439790fea8bf18dc6c8e4"], "isAsync": true, "name": "", "desc": ""}, "preScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}}, "nextAssigneeSetting": {"nextAssignees": [], "enabled": false}, "suspendSetting": {"isAutoActivate": false, "activateTime": 1}, "rejectNodes": [], "allowedOps": ["done", "SLAChange"], "userTaskId": "Activity_0w88z0a"}, "assigneeValue": "", "approveType": "single", "isFirst": false, "subProcess": {"subProcessId": "", "isSub": false}, "id": "Activity_0w88z0a", "isLast": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Activity_1giay94", "name": "跳转至申请人验收"}]}, {"assigneeListUser": ["<PERSON><PERSON><PERSON>"], "isFormDecision": "0", "handling": "directly", "name": "申请人验收", "links": {"outgoing": ["Flow_0k4zi6h"], "incoming": ["Flow_1coss7g"]}, "isNextPar": false, "skipStragety": "emptyAssign", "formExpressionName": "", "assigneeGroups": [], "userType": "specifyUser", "countersignRate": 0, "Setting": {"candidateSettings": [], "triggerIdList": ["5c9a95cd583c2"], "revokeRoles": ["admin"], "labelViews": [], "holidayHandleConf": {"enabled": false, "groupId": ""}, "userStrategyId": "", "cancelRoles": ["admin"], "memoLevel": 0, "scriptSettings": {"postScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}, "preScript": {"operations": [], "scriptIdList": [], "isAsync": true, "name": "", "desc": ""}}, "nextAssigneeSetting": {"nextAssignees": [], "enabled": false}, "suspendSetting": {"isAutoActivate": false, "activateTime": 1}, "rejectNodes": [], "allowedOps": ["done", "SLAChange"], "userTaskId": "Activity_1giay94"}, "assigneeValue": "", "approveType": "single", "isFirst": false, "subProcess": {"subProcessId": "", "isSub": false}, "id": "Activity_1giay94", "isLast": false, "processOp": [{"isSubProcess": false, "conditionExpression": {"name": "pass", "value": "0"}, "targetTaskId": "Event_1asihg4", "name": "跳转至结束"}]}], "ctime": "2025-05-19 14:26:08", "toolStatus": "", "nrOfInstances": 0, "isTimeout": false, "oldProcessInstanceTaskStruct": null, "slaStatus": "", "action": "", "processVersion": {"instanceId": "63485da1e068e", "bpmnXML": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<bpmn2:definitions xmlns:bpmn2=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" id=\"sample-diagram\" targetNamespace=\"http://bpmn.io/schema/bpmn\" xsi:schemaLocation=\"http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd\">\n  <bpmn2:process id=\"ITSC-PROCESS-ID\" name=\"ITSC-PROCESS-NAME\">\n    <bpmn2:documentation>描述</bpmn2:documentation>\n    <bpmn2:startEvent id=\"StartEvent_1\" name=\"开始\">\n      <bpmn2:outgoing>Flow_1qmhbru</bpmn2:outgoing>\n    </bpmn2:startEvent>\n    <bpmn2:sequenceFlow id=\"Flow_1qmhbru\" sourceRef=\"StartEvent_1\" targetRef=\"Activity_0xuq1we\" />\n    <bpmn2:userTask id=\"Activity_0xuq1we\" name=\"发起申请\" flowable:isFormDecision=\"1\" flowable:strategy=\"emptyAssign\" flowable:handling=\"directly\" flowable:dispatchStrategy=\"\" flowable:assignee=\"miaofeng\" flowable:subsequentConf=\"[]\" flowable:setAssignee=\"false\" flowable:formExpressionName=\"os_type:Activity_0xuq1we.h5k3buccoh.0.h5k3buccol.value\" flowable:assigneeValue=\"\" flowable:assigneeType=\"\" flowable:assigneeList=\"\" flowable:assigneeGroup=\"\">\n      <bpmn2:incoming>Flow_1qmhbru</bpmn2:incoming>\n      <bpmn2:outgoing>Flow_0aa5jdl</bpmn2:outgoing>\n    </bpmn2:userTask>\n    <bpmn2:sequenceFlow id=\"Flow_0aa5jdl\" sourceRef=\"Activity_0xuq1we\" targetRef=\"Gateway_0bnlaxl\" />\n    <bpmn2:userTask id=\"Activity_19sgyi6\" name=\"windows管理员审批\" flowable:isFormDecision=\"0\" flowable:strategy=\"emptyAssign\" flowable:handling=\"directly\" flowable:dispatchStrategy=\"\" flowable:assignee=\"miaofeng\" flowable:subsequentConf=\"[]\" flowable:setAssignee=\"true\" flowable:formExpressionName=\"\" flowable:assigneeValue=\"\" flowable:assigneeType=\"\" flowable:assigneeList=\"\" flowable:assigneeGroup=\"\">\n      <bpmn2:incoming>Flow_1rxnxwy</bpmn2:incoming>\n      <bpmn2:outgoing>Flow_12cpyu4</bpmn2:outgoing>\n    </bpmn2:userTask>\n    <bpmn2:userTask id=\"Activity_0w88z0a\" name=\"实施\" flowable:isFormDecision=\"0\" flowable:strategy=\"emptyAssign\" flowable:handling=\"directly\" flowable:dispatchStrategy=\"\" flowable:assignee=\"\" flowable:assigneeValue=\"\" flowable:assigneeType=\"\" flowable:assigneeList=\"\" flowable:assigneeGroup=\"\" flowable:subsequentConf=\"[]\" flowable:setAssignee=\"false\" flowable:formExpressionName=\"\">\n      <bpmn2:incoming>Flow_13mj9t4</bpmn2:incoming>\n      <bpmn2:outgoing>Flow_1coss7g</bpmn2:outgoing>\n    </bpmn2:userTask>\n    <bpmn2:sequenceFlow id=\"Flow_1coss7g\" sourceRef=\"Activity_0w88z0a\" targetRef=\"Activity_1giay94\" />\n    <bpmn2:userTask id=\"Activity_1giay94\" name=\"申请人验收\" flowable:isFormDecision=\"0\" flowable:strategy=\"emptyAssign\" flowable:handling=\"directly\" flowable:dispatchStrategy=\"\" flowable:assignee=\"miaofeng\" flowable:subsequentConf=\"[]\" flowable:setAssignee=\"false\" flowable:formExpressionName=\"\" flowable:assigneeValue=\"\" flowable:assigneeType=\"\" flowable:assigneeList=\"\" flowable:assigneeGroup=\"\">\n      <bpmn2:incoming>Flow_1coss7g</bpmn2:incoming>\n      <bpmn2:outgoing>Flow_0k4zi6h</bpmn2:outgoing>\n    </bpmn2:userTask>\n    <bpmn2:endEvent id=\"Event_1asihg4\">\n      <bpmn2:incoming>Flow_0k4zi6h</bpmn2:incoming>\n    </bpmn2:endEvent>\n    <bpmn2:sequenceFlow id=\"Flow_0k4zi6h\" sourceRef=\"Activity_1giay94\" targetRef=\"Event_1asihg4\" />\n    <bpmn2:exclusiveGateway id=\"Gateway_0bnlaxl\">\n      <bpmn2:incoming>Flow_0aa5jdl</bpmn2:incoming>\n      <bpmn2:outgoing>Flow_1rxnxwy</bpmn2:outgoing>\n      <bpmn2:outgoing>Flow_1q9akd7</bpmn2:outgoing>\n    </bpmn2:exclusiveGateway>\n    <bpmn2:sequenceFlow id=\"Flow_1rxnxwy\" sourceRef=\"Gateway_0bnlaxl\" targetRef=\"Activity_19sgyi6\">\n      <bpmn2:conditionExpression xsi:type=\"bpmn2:tFormalExpression\">${os_type == \"1\"}</bpmn2:conditionExpression>\n    </bpmn2:sequenceFlow>\n    <bpmn2:sequenceFlow id=\"Flow_1q9akd7\" sourceRef=\"Gateway_0bnlaxl\" targetRef=\"Activity_0zr93wj\">\n      <bpmn2:conditionExpression xsi:type=\"bpmn2:tFormalExpression\">${os_type == \"2\"}</bpmn2:conditionExpression>\n    </bpmn2:sequenceFlow>\n    <bpmn2:userTask id=\"Activity_0zr93wj\" name=\"linux管理员审批\" flowable:isFormDecision=\"0\" flowable:strategy=\"emptyAssign\" flowable:handling=\"directly\" flowable:dispatchStrategy=\"\" flowable:assignee=\"miaofeng\" flowable:subsequentConf=\"[]\" flowable:setAssignee=\"false\" flowable:formExpressionName=\"\" flowable:assigneeValue=\"\" flowable:assigneeType=\"\" flowable:assigneeList=\"\" flowable:assigneeGroup=\"\">\n      <bpmn2:incoming>Flow_1q9akd7</bpmn2:incoming>\n      <bpmn2:outgoing>Flow_06szss7</bpmn2:outgoing>\n    </bpmn2:userTask>\n    <bpmn2:exclusiveGateway id=\"Gateway_1yw8w9z\">\n      <bpmn2:incoming>Flow_06szss7</bpmn2:incoming>\n      <bpmn2:incoming>Flow_12cpyu4</bpmn2:incoming>\n      <bpmn2:outgoing>Flow_13mj9t4</bpmn2:outgoing>\n    </bpmn2:exclusiveGateway>\n    <bpmn2:sequenceFlow id=\"Flow_06szss7\" sourceRef=\"Activity_0zr93wj\" targetRef=\"Gateway_1yw8w9z\" />\n    <bpmn2:sequenceFlow id=\"Flow_12cpyu4\" sourceRef=\"Activity_19sgyi6\" targetRef=\"Gateway_1yw8w9z\" />\n    <bpmn2:sequenceFlow id=\"Flow_13mj9t4\" sourceRef=\"Gateway_1yw8w9z\" targetRef=\"Activity_0w88z0a\" />\n  </bpmn2:process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_1\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_1\" bpmnElement=\"ITSC-PROCESS-ID\">\n      <bpmndi:BPMNEdge id=\"Flow_13mj9t4_di\" bpmnElement=\"Flow_13mj9t4\">\n        <di:waypoint x=\"625\" y=\"82\" />\n        <di:waypoint x=\"700\" y=\"82\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_12cpyu4_di\" bpmnElement=\"Flow_12cpyu4\">\n        <di:waypoint x=\"520\" y=\"170\" />\n        <di:waypoint x=\"600\" y=\"170\" />\n        <di:waypoint x=\"600\" y=\"107\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_06szss7_di\" bpmnElement=\"Flow_06szss7\">\n        <di:waypoint x=\"520\" y=\"0\" />\n        <di:waypoint x=\"600\" y=\"0\" />\n        <di:waypoint x=\"600\" y=\"57\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1q9akd7_di\" bpmnElement=\"Flow_1q9akd7\">\n        <di:waypoint x=\"340\" y=\"57\" />\n        <di:waypoint x=\"340\" y=\"0\" />\n        <di:waypoint x=\"420\" y=\"0\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1rxnxwy_di\" bpmnElement=\"Flow_1rxnxwy\">\n        <di:waypoint x=\"340\" y=\"107\" />\n        <di:waypoint x=\"340\" y=\"170\" />\n        <di:waypoint x=\"420\" y=\"170\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0k4zi6h_di\" bpmnElement=\"Flow_0k4zi6h\">\n        <di:waypoint x=\"950\" y=\"82\" />\n        <di:waypoint x=\"1034\" y=\"82\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1coss7g_di\" bpmnElement=\"Flow_1coss7g\">\n        <di:waypoint x=\"800\" y=\"82\" />\n        <di:waypoint x=\"850\" y=\"82\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0aa5jdl_di\" bpmnElement=\"Flow_0aa5jdl\">\n        <di:waypoint x=\"270\" y=\"82\" />\n        <di:waypoint x=\"315\" y=\"82\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1qmhbru_di\" bpmnElement=\"Flow_1qmhbru\">\n        <di:waypoint x=\"128\" y=\"82\" />\n        <di:waypoint x=\"170\" y=\"82\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNShape id=\"_BPMNShape_StartEvent_2\" bpmnElement=\"StartEvent_1\">\n        <dc:Bounds x=\"92\" y=\"64\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"99\" y=\"40\" width=\"22\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_02an1c0_di\" bpmnElement=\"Activity_0xuq1we\">\n        <dc:Bounds x=\"170\" y=\"42\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_0vry1kl_di\" bpmnElement=\"Activity_19sgyi6\">\n        <dc:Bounds x=\"420\" y=\"130\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_0g087z1_di\" bpmnElement=\"Activity_0w88z0a\">\n        <dc:Bounds x=\"700\" y=\"42\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_0gf3gqf_di\" bpmnElement=\"Activity_1giay94\">\n        <dc:Bounds x=\"850\" y=\"42\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_1asihg4_di\" bpmnElement=\"Event_1asihg4\">\n        <dc:Bounds x=\"1032\" y=\"72\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Gateway_0bnlaxl_di\" bpmnElement=\"Gateway_0bnlaxl\" isMarkerVisible=\"true\">\n        <dc:Bounds x=\"315\" y=\"57\" width=\"50\" height=\"50\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_0zu81a9_di\" bpmnElement=\"Activity_0zr93wj\">\n        <dc:Bounds x=\"420\" y=\"-40\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Gateway_1yw8w9z_di\" bpmnElement=\"Gateway_1yw8w9z\" isMarkerVisible=\"true\">\n        <dc:Bounds x=\"575\" y=\"57\" width=\"50\" height=\"50\" />\n      </bpmndi:BPMNShape>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</bpmn2:definitions>", "isJumpable": true, "versionName": "1.0.3"}, "userTaskId": "Activity_0w88z0a"}
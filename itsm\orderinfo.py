import json
import os

# 读取配置文件
with open(os.path.join(os.path.dirname(__file__), 'orderinfo.json'), 'r') as f:
    data = json.load(f) # 将json格式的数据转换为python字典，以便后续操作

# 将python的数据类型转换为json字符串
# print(json.dumps(data, ensure_ascii=False, indent=2))

# print(data['userTaskInfo'][0])
print(data['userTaskInfo'][0]['status'])
print(data['userTaskInfo'][0]['taskName'])
print(data['userTaskInfo'][0]['userTaskId'])
print("流程ID：", data['processInstance']['instanceId'])

# 打印stepList列表
# print(json.dumps(data['stepList'], ensure_ascii=False, indent=2))
print("stepList列表的元素个数为：",  len(data['stepList']))
# print("stepList列表的第一个元素：", data['stepList'][0])
# print("stepList列表的第二个元素：", data['stepList'][1])
# print("stepList列表的第三个元素：", data['stepList'][2])
# print("stepList列表的第四个元素：", data['stepList'][3])


for step in data["stepList"]:
    # print(step['userTaskId'])
    if step["userTaskId"] == "Activity_0xuq1we":
        print(step["formData"])
        print(type(step["formData"]))    # <class 'str'>
        """
        eval不安全，因为它会运行任何代码
        当你执行eval(s) 时，就等同于直接写：lst = [{'a': 1, 'b': 2}]
        相当于在Python交互式解释器(IDLE)里，回车执行它会返回[{'a': 1, 'b': 2}]
        表达式 是“有值”的，表达式写出来就能被计算出一个结果（值）,eval()可以执行；
        表达式：能“产生一个值”的代码，比如计算、函数调用、数据结构字面量等。
        语句 是“做事”的，eval() 不行，但可以用 exec() 执行
        语句：完成某种操作或控制程序流程，比如赋值、循环、条件、函数定义等。
        总结：
        1、eval()可以把“长得像Python表达式”的字符串，直接转成对应的Python对象
        2、python表达式可以理解为在交互式解释器(IDLE)里,回车执行后有值返回的
        注意：如果formData是标准的JSON字符串, 推荐用json.loads()，更安全    
        """
        # 把一个“表达式字符串”当作Python代码执行，并返回它的值。
        # 相当于在Python交互式解释器直接回车执行这个列表并返回值，返回值的再赋值给formdata变量
        formdata = eval(step["formData"])
        print(type(formdata))  # <class 'list'>
        # 如果formData是标准的JSON字符串，推荐使用json.loads,更安全
        # formdata = json.loads(step["formData"])
        


# 遍历stepList列表
for step in data['stepList']:
    print(len(step['formData']))
    """
    formData有几种结果:
    1、"formData": ""   空    字符长度为0
    2、"formData": "[]"  空列表  字符长度为2
    3、"formData": "[{}]"  非空列表
    """ 
    # if 'formData' and len(step['formData']) > 2:
    if len(step['formData']) > 2:    
        print(step['formData'])  # formData为一个列表
        print(step['taskName'])
        # data = step['formData'] # 返回的是json数组对象，需要先转换为python对象
        # print(data)
        data = json.loads(step['formData']) # 将json数组对象转换为python列表
        # print(data)
        print('元素1----------------')
        print(data[0])
        print(data[0]['values'])
        print(data[0]['values'][0]['h5k3buccom'][0]['instanceId'])
        print(data[0]['values'][0]['h5k3buccon'][1]['name'])

        # print('元素2----------------')
        # print(data[1])
        print('元素3----------------')
        # print(data[2])
        list_data = data[2]['values']
        print(list_data)
        print('------------------')
        print(len(list_data))
        # 获取checksum值
        checksum_value = list_data[1]['h5kupa8hmj'][0]['checksum']
        print(checksum_value)
        # 下标固定的的取值方式
        for entry in list_data:
        # 如果 value 是列表，就继续往下找
            # 遍历entry字典的所有value
            for v in entry.values():
                print(v)
                # isinstance() 是 Python 的内置函数，用于判断一个对象是否是某种类型
                if isinstance(v, list):
                    for item in v:
                        if 'checksum' in item:
                            print(item['checksum'])

        # print('元素4----------------')
        # print(data[3])
        # print(data[3]['key'])
        # print(data[3]['values'])

    else:
        print(step['taskName'], "无表单数据")    


# print(f"当前流程阶段: {data['userTaskInfo'][0]['taskName']}")
# print(f"流程ID: {data['processInstance']['instanceId']}")
#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-
import sys
import json
import requests
import os

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

# 服务器主机名
HOST = "***********"
EASYOPS_ORG = EASYOPS_ORG  # 从环境变量获取

def upload_workflow_file(filepath, process_instance_id=None, file_category="original"):
    """
    上传文件到流程表单（使用multipart/form-data方式）
    
    参数:
    filepath - 要上传的文件路径
    process_instance_id - 流程实例ID（可选）
    file_category - 文件类别，可选值:
                    - original (原有的)
                    - added (新增的，当为added时需要提供processInstanceId)
    
    返回:
    上传响应的JSON结果
    """
    # 构建请求URL
    url = "http://{0}:8134/api/flowable_service/v1/file".format(HOST)
    
    # 设置请求头（不需要指定Content-Type，requests会自动设置为multipart/form-data）
    headers = {
        "user": "easyops", 
        "org": str(EASYOPS_ORG)
    }
    
    # 准备请求数据
    data = {
        "fileCategory": file_category
    }
    
    # 如果文件类别为added，必须提供流程实例ID
    if file_category == "added" and not process_instance_id:
        print "错误: 当fileCategory为'added'时，必须提供processInstanceId"
        return {}
    
    # 如果提供了流程实例ID，则添加到请求数据中
    if process_instance_id:
        data["processInstanceId"] = process_instance_id
    
    # 打开文件准备上传
    filename = os.path.basename(filepath)
    files = {"file": (filename, open(filepath, "rb"))}
    
    # 发送POST请求上传文件（requests会自动使用multipart/form-data格式）
    response = requests.post(url, headers=headers, data=data, files=files)
    
    print "上传响应状态码:", response.status_code
    print "上传响应内容:", response.text
    
    # 处理响应结果
    if response.status_code == 200:
        return response.json()
    else:
        print "文件上传失败"
        return {}

if __name__ == "__main__":
    # 测试上传文件
    file_path = "/tmp/test05081127.txt"  # 替换为实际文件路径
    
    if os.path.exists(file_path):
        # 示例1: 上传原始文件（不需要流程实例ID）
        result1 = upload_workflow_file(file_path, file_category="original")
        
        # 示例2: 上传新增文件（需要流程实例ID）
        process_id = "your_process_instance_id"  # 替换为实际流程实例ID
        result2 = upload_workflow_file(file_path, process_instance_id=process_id, file_category="added")
        
        if result1:
            print "\n原始文件上传成功:"
            print json.dumps(result1, indent=2, ensure_ascii=False)
            
        if result2:
            print "\n新增文件上传成功:"
            print json.dumps(result2, indent=2, ensure_ascii=False)
    else:
        print "文件不存在，请检查路径"
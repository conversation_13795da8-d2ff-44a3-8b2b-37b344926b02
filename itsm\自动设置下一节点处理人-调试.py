
#--*--coding:utf8--*-- 
import json 
import requests

# host = EASYOPS_CMDB_HOST.split(":")[0]
host = "***********"
EASYOPS_ORG = "2024021401"

def cmdb_search():
    url = "http://{}/object/EVENTTYPE/instance/_search".format(host)
    headers = {"user":"easyops","org":str(EASYOPS_ORG),"Content-Type":"application/json","host":"cmdb_resource.easyops-only.com"}
    params = {
        "query":{
            "name":{
                "$eq": "信息技术总部网络安全组问题"
            }
        }
    }
    response = requests.request("POST",url,headers=headers,data=json.dumps(params))
    # print(response.status_code)
    # print(response.text)
    print(type(response.text)) # <class 'str'>
    # 因为返回的结果是字符串，所以需要现将字符串转换为python对象
    # print(json.loads(response.text))
    print(type(json.loads(response.text)))  # <class 'dict'>
    """
    思考为什么要先用json.loads()把response.text转成Python对象（通常是 dict），然后用json.dumps()格式化打印？
    response.text 是一个字符串（str），而 json.dumps() 的作用是把Python对象（如 dict、list 等）序列化为 JSON 字符串。
    如果你直接对字符串用json.dumps()，它只会把这个字符串再加一层引号，变成合法的 JSON 字符串，而不会格式化里面的内容。
    也就是说json.dumps只能把python中的字典或者列表转为json字符串，其它数据类型不行。
    json.loads()的作用是把JSON格式的字符串解析成 Python 对象（如 dict、list 等）。
    JSON 格式的字符串是一种文本（字符串），内容符合JSON语法规范。
    总结：
    1、json.dumps() 只能格式化 Python 对象，不能直接格式化 JSON 字符串。
    2、必须先用json.loads()把字符串转成对象，再格式化输出。
    """
    # print(json.dumps(json.loads(response.text), ensure_ascii=False, indent=2))
    print(type(json.dumps(json.loads(response.text))))
    # 推荐使用response.json()
    # response.json()等价于json.loads(response.text)，都是把响应内容解析成Python对象（通常是 dict）
    print(json.dumps(response.json(), ensure_ascii=False, indent=2))
    # print(json.dumps(response.text, ensure_ascii=False, indent=2))

    print(response.json()['data']['list'])
    print(type(response.json()['data']['list'])) # <class 'list'>
    print(len(response.json()['data']['list']))
    
    
    if response.status_code == 200:
        return response.json()["data"]["list"]
    else:
        return []
        
if __name__ == '__main__':
    ret = cmdb_search()
    # print json.dumps(ret, ensure_ascii=False, indent=2)
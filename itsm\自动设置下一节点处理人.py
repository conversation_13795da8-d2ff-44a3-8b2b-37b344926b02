#!/usr/local/easyops/python/bin/python 
#--*--coding:utf8--*-- 
import json 
import requests

host = EASYOPS_CMDB_HOST.split(":")[0]


# 去EVENTTYPE(事件类别)模型，查询name名称等于
def cmdb_search(eventtypes):
    url = "http://{}/object/EVENTTYPE/instance/_search".format(host)
    headers = {"user":"easyops","org":str(EASYOPS_ORG),"Content-Type":"application/json","host":"cmdb_resource.easyops-only.com"}
    params = {
        "query":{
            "name":{
                "$in":eventtypes
            }
        }
    }
    response = requests.request("POST",url,headers=headers,data=json.dumps(params))
    if response.status_code == 200:
        return response.json()["data"]["list"]
    else:
        return []
    

if __name__ == '__main__':
    #print orderInfo
    instanceIds = []
    orderInfo = json.loads(orderInfo)
    print json.dumps(orderInfo,indent=2,ensure_ascii=False)
    event_type_name = []
    

    # 遍历stepList列表
    for step in orderInfo["stepList"]:
        if step["userTaskId"] == "Activity_14jgyo8":
            formdata = eval(step["formData"])
    for i in formdata:
        if i["key"] == "h3r2exb5mh":
            for value in i["values"]:
                eventType = value["eventType"]
                for x in eventType:
                    event_type_name.append(x["name"])

    ret = cmdb_search(event_type_name)
    for i in ret:
        groups = i["GROUP"]
        if groups:
            for group in groups:
                instanceIds.append(group["instanceId"])
    #print "assigneeGroups",":{}".format(",".join(instanceIds))
    PutStr("assigneeGroups",":{}".format(",".join(instanceIds)))
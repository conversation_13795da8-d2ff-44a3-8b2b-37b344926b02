#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import requests
import json

# 全局请求头
HEADERS = {
    'org': str(EASYOPS_ORG),
    'user': 'easyops',
    'Content-Type': 'application/json',
    'host': 'cmdb_resource.easyops-only.com'
}

def register_user(username, password, email, org_id, is_admin=False, nickname=''):
    """
    注册用户函数
    
    参数:
    username - 用户名
    password - 密码
    email - 邮箱
    org_id - 组织ID(整数)
    is_admin - 是否为管理员(布尔值)，默认为False
    nickname - 用户昵称
    
    返回:
    响应状态码和响应文本
    """
    # API地址和请求方法
    url = "http://***********:8111/api/v1/users/register"
    method = "POST"
    
    # 请求体
    payload = {
        'name': username,
        'password': password,
        'email': email,
        'org': org_id,
        'isAdmin': is_admin,
        'nickname': nickname
    }
    
    # 发送请求
    response = requests.request(method, url, headers=HEADERS, data=json.dumps(payload))
    
    # 返回状态码和响应文本
    return response.status_code, response.text

def create_workflow(service_id, name, visible_range="operator"):
    """
    发起工单流程函数
    
    参数:
    service_id - 服务ID
    name - 流程名称
    visible_range - 可见范围，默认为"operator"
    
    返回:
    响应状态码和响应文本
    """
    # API地址和请求方法
    url = "http://***********:8134/api/flowable_service/v2/process_instance"
    method = "POST"
    
    # 请求体
    payload = {
        "serviceId": service_id,
        "name": name,
        "visibleRange": visible_range
        # "relevanceInstanceId":"",
        # 'formData': "[]"  # 空的表单数据对象
    }
    
    # 发送请求
    response = requests.request(method, url, headers=HEADERS, data=json.dumps(payload))
    
    # 返回状态码和响应文本
    return response.status_code, response.text

def delete_user(username):
    """
    删除用户函数
    
    参数:
    username - 要删除的用户名
    
    返回:
    响应状态码和响应文本
    """
    # API地址和请求方法
    url = "http://***********:8111/api/v1/users/" + username
    method = "DELETE"
    
    # 发送请求
    response = requests.request(method, url, headers=HEADERS)
    
    # 返回状态码和响应文本
    return response.status_code, response.text

if __name__ == "__main__":
#     # 调用注册函数
#     status_code, response_text = register_user(
#         username='testuser05061651', 
#         password='testuser0506', 
#         email='<EMAIL>', 
#         org_id=2024021401,
#         is_admin=False,
#         nickname='用户注册测试8'
#     )
#     # 打印状态码
#     print(status_code)
#     # 打印响应文本
#     # print(response_text)
#     # 尝试格式化输出JSON结果
#     print json.dumps(json.loads(response_text), indent=2, ensure_ascii=False)

    # 调用删除用户函数
    status_code, response_text = delete_user('testuser05061651')
    print status_code
    print json.dumps(json.loads(response_text), indent=2, ensure_ascii=False)



    # 调用发起工单流程函数
    # status_code, response_text = create_workflow(       # 如果流程本身有问题，会报错提示500，但是发起是成功的
    #     service_id="631b13da8f11f",
    #     name="主机申请测试"
    # )
    # status_code, response_text = create_workflow(
    #     service_id="6335a9e4f87da",
    #     name="表单测试"
    # )    
    # print status_code
    # print json.dumps(json.loads(response_text), indent=2, ensure_ascii=False)

# list1 = [
#   {
#     "_pre_ts": 1737685282, 
#     "taxInclusiveAmount": "13900.00", 
#     "creator": "easyops", 
#     "instanceId": "62c469f565a79", 
#     "purchaseDate": "2021-09-06 00:00:00", 
#     "updateAuthorizers": [], 
#     "_object_id": "ASSETS", 
#     "mtime": "2025-02-07 10:58:25", 
#     "_impl_from": [], 
#     "supplier": "None", 
#     "personLiable": "None", 
#     "assetRecipient": "梁东全", 
#     "financialStorageLocation": "信息技术总部", 
#     "readAuthorizers": [], 
#     "department": "信息技术总部", 
#     "originalAssetValue": "12300.88", 
#     "assetSpecifications": "H3C MSR3610-X1-DP", 
#     "_ts": 1738897105, 
#     "deleteAuthorizers": [], 
#     "assetNumber": "10085654", 
#     "assetResponsiblePerson": "梁东全", 
#     "physicalStorageLocation": "上海金桥数据中心", 
#     "org": 2024021401, 
#     "ctime": "2025-01-22 15:41:32", 
#     "name": "报盘和外联接入路由器", 
#     "assetClassification": "固定资产", 
#     "_version": 4, 
#     "modifier": "easyops", 
#     "_object_version": 28, 
#     "assetStatus": "正常使用", 
#     "quantity": "1"
#   }
# ]

# print(list1)
# print(list1[0])
# # for i in list1:
# #     print(i)
# #     for x in i.items():
# #         if "assetResponsiblePerson" in x:
# #             print(x[0])
# #             print(x[1])
# #             print(x)

# for i in list1[0].items():
#     if "assetResponsiblePerson" in i:
#         # print(i[0])
#         print(i[1])
#         # print(i)


list2 = [
  {
    "instanceId": "63034c0f6b53d", 
    "_object_id": "USER", 
    "name": "000156"
  }
]

for item in list2:
    name = item.get("name")
    print(name)
# for i in list2[0].items():
#     if "assetResponsiblePerson" in i:
#         # print(i[0])
#         print(i[1])
#         # print(i)

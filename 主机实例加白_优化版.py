#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json
import time
import logging
from datetime import datetime

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('主机实例加白_执行日志.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 禁用requests库的HTTP连接详细日志
logging.getLogger('requests.packages.urllib3.connectionpool').setLevel(logging.WARNING)

# 时间统计类
class TimeTracker:
    def __init__(self):
        self.times = {}
        self.start_times = {}
        self.function_times = {}  # 存储每个函数的总执行时间

    def start_timer(self, name):
        """开始计时"""
        self.start_times[name] = time.time()
        # logger.info("开始执行: {}".format(name))  # 注释掉详细的开始执行日志

    def end_timer(self, name):
        """结束计时"""
        if name in self.start_times:
            elapsed = time.time() - self.start_times[name]
            self.times[name] = elapsed
            # logger.info("完成执行: {}, 耗时: {:.2f}秒".format(name, elapsed))  # 注释掉详细的完成执行日志

            # 统计函数级别的总执行时间
            function_name = self._extract_function_name(name)
            if function_name not in self.function_times:
                self.function_times[function_name] = {'total_time': 0, 'call_count': 0}
            self.function_times[function_name]['total_time'] += elapsed
            self.function_times[function_name]['call_count'] += 1

            return elapsed
        return 0

    def _extract_function_name(self, timer_name):
        """从计时器名称中提取函数名"""
        if "CMDB实例搜索" in timer_name:
            return "cmdb_instance_search"
        elif "更新实例权限" in timer_name:
            return "update_instance_permissions"
        elif "查询应用系统" in timer_name:
            return "cmdb_instance_search(APP_SYSTEM)"
        else:
            return timer_name

    def get_summary(self):
        """获取时间统计摘要"""
        # logger.info("=" * 70)
        # logger.info("执行时间统计摘要")
        # logger.info("=" * 70)

        # 显示所有计时项目
        # logger.info("详细执行时间:")
        # logger.info("-" * 70)
        total_time = 0
        for _, elapsed in self.times.items():
            # logger.info("{}: {:.2f}秒".format(name, elapsed))  # 注释掉详细执行时间
            total_time += elapsed

        logger.info("-" * 70)
        logger.info("各函数总执行耗时:")
        logger.info("-" * 70)

        # 显示每个函数的总执行时间
        for func_name, stats in self.function_times.items():
            total_func_time = stats['total_time']
            call_count = stats['call_count']
            avg_time = total_func_time / call_count if call_count > 0 else 0
            logger.info("{}: 总耗时 {:.2f}秒, 调用次数 {}, 平均耗时 {:.2f}秒".format(
                func_name, total_func_time, call_count, avg_time))

        logger.info("-" * 70)
        # logger.info("脚本总执行时间: {:.2f}秒".format(total_time))  # 注释掉重复的脚本总执行时间
        logger.info("=" * 70)
        return self.times

# 全局时间追踪器
timer = TimeTracker()

# Python 2/3 兼容性处理
if sys.version_info[0] == 2:
    reload(sys)
    sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    """原有的cmdb_instance_search函数，添加时间统计"""
    timer_name = "CMDB实例搜索_{}".format(object_id)
    timer.start_timer(timer_name)

    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break

    timer.end_timer(timer_name)
    return ret_list

def update_instance_permissions(object_id, instance_ids, groups, fields, method=""):
    """原有的update_instance_permissions函数，添加时间统计"""
    timer_name = "更新实例权限_{}".format(instance_ids)
    timer.start_timer(timer_name)

    url = "http://{}:8079/permission/{}/instances/_batch".format(HOST, object_id)
    # 注意请求体的格式，除了method，其它字段的值必须是列表格式
    payload = {
        "method": method,
        "list": [groups],
        "fields": fields,
        "ids": [instance_ids]
    }
    # print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)
    # response = requests.put(url, headers=headers, data=json.dumps(payload))
    # return response

    timer.end_timer(timer_name)

if __name__ == "__main__":
    # 脚本开始执行
    script_start_time = time.time()
    logger.info("=" * 60)
    logger.info("开始执行主机实例加白脚本")
    logger.info("执行时间: {}".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
    logger.info("=" * 60)

    # 原有的主逻辑保持完全不变，只添加时间统计
    timer.start_timer("脚本初始化")
    object_id = "HOST"
    relation_id = "system" # 主机和业务系统的关系ID
    method = "overwrite"
    # method = "append"
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    params = {
        "fields": {
            "instanceId": True,
            relation_id: True
        },
        "query": {
            "$and": [
                {relation_id: {"$exists": True}}
            ]
        }
    }
    timer.end_timer("脚本初始化")

    # 查询主机实例
    timer.start_timer("查询主机实例")
    instances = cmdb_instance_search(object_id, params)
    # print json.dumps(instances, ensure_ascii=False, indent=2)
    # print "查询到的满足条件的实例总数:", len(instances)
    timer.end_timer("查询主机实例")

    # 处理每个实例
    timer.start_timer("处理所有实例权限")
    processed_count = 0

    for i in instances:
        instance_start = time.time()
        # print json.dumps(i, ensure_ascii=False, indent=2)
        instanceId = i["instanceId"]
        # print instanceId
        name = i["system"][0]["name"]

        # 查询应用系统
        app_timer_name = "查询应用系统_{}".format(name)
        timer.start_timer(app_timer_name)
        params = {"fields": {"group": True}, "query": {"name": {"$eq": name}}}
        ret = cmdb_instance_search("APP_SYSTEM@ONEMODEL", params)
        timer.end_timer(app_timer_name)

        # print json.dumps(ret, ensure_ascii=False, indent=2)
        if ret and ret[0].get("group") and len(ret[0]["group"]) > 0:
        # if ret:
            # print json.dumps(ret[0]["group"][0]["name"], ensure_ascii=False, indent=2)
            group_name = ret[0]["group"][0]["name"]
            result = update_instance_permissions(
                object_id=object_id,
                instance_ids=instanceId,
                groups=group_name,
                fields=fields,
                method=method
                )
            # print "状态码:", result.status_code
            # print "响应内容:"
            # try:
            #     print json.dumps(result.json(), indent=2, ensure_ascii=False)
            # except:
            #     print result.text

        processed_count += 1
        instance_elapsed = time.time() - instance_start
        # logger.info("实例 {} 处理完成，耗时: {:.2f}秒".format(instanceId, instance_elapsed))  # 注释掉单个实例处理日志

        # 根据内存中的设置，每处理batch个实例休息一下
        batch = 30
        if processed_count % batch == 0:
            logger.info("已处理 {}个实例，休息0.5秒...".format(processed_count))
            time.sleep(0.5)

    timer.end_timer("处理所有实例权限")

    # 脚本执行完成统计
    script_end_time = time.time()
    total_script_time = script_end_time - script_start_time

    logger.info("=" * 60)
    logger.info("脚本执行完成")
    logger.info("处理实例总数: {}".format(processed_count))
    logger.info("脚本总执行时间: {:.2f}秒".format(total_script_time))
    logger.info("平均每个实例处理时间: {:.2f}秒".format(total_script_time / max(processed_count, 1)))

    # 显示详细的时间统计
    timer.get_summary()
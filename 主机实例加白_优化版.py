#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list

def update_instance_permissions(object_id, instance_ids, groups, fields, method=""):
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    # 注意请求体的格式，除了method，其它字段的值必须是列表格式
    payload = {
        "method": method,
        "list": [groups],
        "fields": fields,
        "ids": [instance_ids]
    }
    print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    return response

if __name__ == "__main__":
    object_id = "HOST"
    relation_id = "system" # 主机和业务系统的关系ID
    method = "overwrite"
    # method = "append"
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    params = {
        "fields": {
            "instanceId": True,
            relation_id: True
        },
        "query": {
            "$and": [
                {relation_id: {"$exists": True}}
            ]
        }
    }
    instances = cmdb_instance_search(object_id, params)
    print json.dumps(instances, ensure_ascii=False, indent=2)
    print "查询到的满足条件的实例总数:", len(instances)
    
    for i in instances:
        # print json.dumps(i, ensure_ascii=False, indent=2)
        instanceId = i["instanceId"]
        print instanceId
        name = i["system"][0]["name"]
        params = {"fields": {"group": True}, "query": {"name": {"$eq": name}}}
        ret = cmdb_instance_search("APP_SYSTEM@ONEMODEL", params)
        # print json.dumps(ret, ensure_ascii=False, indent=2)
        if ret:
            print json.dumps(ret[0]["group"][0]["name"], ensure_ascii=False, indent=2)
            group_name = ret[0]["group"][0]["name"]
            result = update_instance_permissions(
                object_id=object_id, 
                instance_ids=instanceId,
                groups=group_name, 
                fields=fields, 
                method=method
                )
            print "状态码:", result.status_code
            print "响应内容:"
            try:
                print json.dumps(result.json(), indent=2, ensure_ascii=False)
            except:
                print result.text
#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json
import time

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    start_time = time.time()
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    end_time = time.time()
    print(u"cmdb_instance_search({}) 查询耗时: {:.3f} 秒".format(object_id, end_time - start_time))
    return ret_list

def update_instance_permissions(object_id, instance_ids, groups, fields, method=""):
    start_time = time.time()
    url = "http://{}:8079/permission/{}/instances/_batch".format(HOST, object_id)
    payload = {
        "method": method,
        "list": [groups],
        "fields": fields,
        "ids": [instance_ids]
    }
    print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)
    # response = requests.put(url, headers=headers, data=json.dumps(payload))
    # end_time = time.time()
    # print(u"update_instance_permissions 执行耗时: {:.3f} 秒".format(end_time - start_time))
    # return response
    end_time = time.time()
    print(u"update_instance_permissions 执行耗时: {:.3f} 秒".format(end_time - start_time))

if __name__ == "__main__":
    total_start = time.time()
    object_id = "HOST"
    relation_id = "system" # 主机和业务系统的关系ID
    method = "overwrite"
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    params = {
        "fields": {
            "instanceId": True,
            relation_id: True
        },
        "query": {
            "$and": [
                {relation_id: {"$exists": True}}
            ]
        }
    }
    t1 = time.time()
    instances = cmdb_instance_search(object_id, params)
    t2 = time.time()
    print json.dumps(instances, ensure_ascii=False, indent=2)
    print "查询到的满足条件的实例总数:", len(instances)
    print(u"主机实例查询耗时: {:.3f} 秒".format(t2 - t1))
    for i in instances:
        instanceId = i["instanceId"]
        print instanceId
        name = i["system"][0]["name"]
        params = {"fields": {"group": True}, "query": {"name": {"$eq": name}}}
        t3 = time.time()
        ret = cmdb_instance_search("APP_SYSTEM@ONEMODEL", params)
        t4 = time.time()
        print(u"APP_SYSTEM@ONEMODEL 查询耗时: {:.3f} 秒".format(t4 - t3))
        if ret and ret[0].get("group") and len(ret[0]["group"]) > 0:
            print json.dumps(ret[0]["group"][0]["name"], ensure_ascii=False, indent=2)
            group_name = ret[0]["group"][0]["name"]
            t5 = time.time()
            update_instance_permissions(
                object_id=object_id, 
                instance_ids=instanceId,
                groups=group_name, 
                fields=fields, 
                method=method
            )
            t6 = time.time()
            print(u"update_instance_permissions 总耗时: {:.3f} 秒".format(t6 - t5))
    total_end = time.time()
    print(u"脚本总耗时: {:.3f} 秒".format(total_end - total_start))

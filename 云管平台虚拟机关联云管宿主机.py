#!/usr/local/easyops/python/bin/python
#--*--coding:utf8--*-- 

import requests
import json 

HOST = EASYOPS_CMDB_HOST.split(':')[0]
headers = {
    'org': str(EASYOPS_ORG),
    'user': 'easyops',
    'Content-Type': 'application/json',
    "host": "cmdb_resource.easyops-only.com"
}

def cmdb_instance_search(objectId, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, objectId)
    ret_list = []
    page = 1
    page_size = 200
    
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list

def import_cmdb(objectId, datas):
    url = "http://{}/object/{}/instance/_import".format(HOST,objectId) # 构造导入接口 URL
    params = {"keys": ["hostId"],"datas": datas}
    # print json.dumps(params, ensure_ascii=False, indent=2)
    response = requests.request("POST", url, headers=headers, data=json.dumps(params))
    print "\ncmdb新增实例响应结果" + "." * 50    
    print json.dumps(json.loads(response.text), indent=2, ensure_ascii=False)
    
def import_batch(objectId, datas, batch):
    for i in range(0, len(datas), batch):
        _datas = datas[i:i+batch]  # 获取当前批次的数据
        import_cmdb(objectId,_datas)  # 调用导入函数    

if __name__ == '__main__':
    params = {"query": {"$and": [{"accountName": {"$nlike": "%阿里云%"}}, {"accountNamehost": {"$exists":True}}]}}
    ret = cmdb_instance_search("CLOUD_PLATFORM", params=params)
    print "帐号名称不包含阿里云且accountNamehost不为空的实例总数:", len(ret)
    # accountNamehostId是联合key，accountName+hostId，按accountNamehostId分组聚合实例ID
    instance_data = {}
    for instance in ret:
        accountNamehostId = instance["accountNamehost"]
        if accountNamehostId not in instance_data:
            instance_data[accountNamehostId] = {
                "CLOUD_HOST_WITH_CLOUD_PLATFORM": [],
                "accountNamehostId": accountNamehostId
            }
        instance_data[accountNamehostId]["CLOUD_HOST_WITH_CLOUD_PLATFORM"].append({"instanceId": instance["instanceId"]})

    datas = list(instance_data.values())
    print "需要导入cmdb的实例总数:", len(datas)
    print json.dumps(datas, ensure_ascii=False, indent=2)
    
    import_batch("CLOUD_HOST", datas, 100) # 分批导入 CMDB
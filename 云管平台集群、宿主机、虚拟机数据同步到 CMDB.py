#!/usr/local/easyops/python/bin/python
#--*--coding:utf8--*-- 

# 导入必要的库
import requests
import json 
import base64
import uuid
import time
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from Crypto.Random import get_random_bytes
from datetime import datetime

# 从环境变量或配置中获取主机地址
HOST = EASYOPS_CMDB_HOST.split(':')[0]

# 获取签名函数
def get_signature(access_key, secret_key):
    res = ""
    # 生成签名的原始字符串，包含 access_key、UUID 和当前时间（毫秒）
    src = access_key + "|" + str(uuid.uuid4()) + "|" + str(int(time.time() * 1000))
    
    try:
        # 将 secret_key 转为字节，并将 access_key 用作 IV（初始化向量）
        key = secret_key.encode('utf-8')
        iv = access_key.encode('utf-8')  # 使用 accessKey 作为 IV

        # 使用 AES 加密方式进行加密
        cipher = AES.new(key, AES.MODE_CBC, iv)
        encrypted = cipher.encrypt(pad(src.encode('utf-8'), AES.block_size))  # 对原始字符串进行填充并加密
        
        # 将加密后的数据转换为 Base64 编码的字符串
        res = base64.b64encode(encrypted).decode('utf-8')
        
    except Exception as e:
        print("Error: {e}")  # 捕获错误并打印

    return res  # 返回生成的签名


# 获取数据函数
def get_datas(accessKey, signature):
    headers = {
        'Content-Type': 'application/json',  # 设置请求内容类型为 JSON
        'Accept': 'application/json',        # 设置接受的数据格式为 JSON
        'accessKey': accessKey,              # 传递 accessKey 到请求头
        'signature': signature               # 传递 signature 到请求头
    }
    
    # 设置请求体内容，这里选择查询实例状态为 Running, Stopped 和 Stopping 的数据
    payload = json.dumps({
        "instanceStatus": [
            "Running",
            "Stopped",
            "Stopping"
        ],
        "selectType": "OTHERS"
    })

    datas = []  # 存储获取的数据
    toPage = 1  # 分页初始值
    
    while True:
        # 构造分页 URL
        url = "http://**********/vm-service/server/list/{}/200".format(toPage)
        print(url)
        
        # 发送 POST 请求获取数据
        response = requests.request("POST", url, headers=headers, data=payload)
        
        if response.status_code == 200:  # 如果请求成功
            total = response.json()["data"]["itemCount"]  # 获取总项数
            datas += response.json()["data"]["listObject"]  # 将返回的数据添加到 datas 中
            
            if len(datas) == total:  # 如果获取的数据已达到总数，结束循环
                break
            toPage += 1  # 否则继续请求下一页
        else:
            print(response.text)  # 打印错误信息
            break
    
    return datas  # 返回所有获取的数据
    
def get_host_datas(accessKey, signature):
    headers = {
        'Content-Type': 'application/json',  # 设置请求内容类型为 JSON
        'Accept': 'application/json',        # 设置接受的数据格式为 JSON
        'accessKey': accessKey,              # 传递 accessKey 到请求头
        'signature': signature               # 传递 signature 到请求头
    }
    
    # 设置请求体内容，这里选择查询实例状态为 Running, Stopped 和 Stopping 的数据
    payload = json.dumps({
        "instanceStatus": [
            "Running",
            "Stopped",
            "Stopping"
        ],
        "selectType": "OTHERS"
    })

    datas = []  # 存储获取的数据
    toPage = 1  # 分页初始值
    
    while True:
        # 构造分页 URL
        url = "http://**********/operation-analytics/host/list/{}/200".format(toPage)
        print(url)
        
        # 发送 POST 请求获取数据
        response = requests.request("POST", url, headers=headers, data=payload)
        
        if response.status_code == 200:  # 如果请求成功
            total = response.json()["data"]["itemCount"]  # 获取总项数
            datas += response.json()["data"]["listObject"]  # 将返回的数据添加到 datas 中
            
            if len(datas) == total:  # 如果获取的数据已达到总数，结束循环
                break
            toPage += 1  # 否则继续请求下一页
        else:
            print(response.text)  # 打印错误信息
            break
    
    return datas  # 返回所有获取的数据

# 获取数据函数
def get_cluster_datas(accessKey, signature):
    headers = {
        'Content-Type': 'application/json',  # 设置请求内容类型为 JSON
        'Accept': 'application/json',        # 设置接受的数据格式为 JSON
        'accessKey': accessKey,              # 传递 accessKey 到请求头
        'signature': signature               # 传递 signature 到请求头
    }
    
    # 设置请求体内容，这里选择查询实例状态为 Running, Stopped 和 Stopping 的数据
    payload = json.dumps({
        "instanceStatus": [
            "Running",
            "Stopped",
            "Stopping"
        ],
        "selectType": "OTHERS"
    })

    datas = []  # 存储获取的数据
    toPage = 1  # 分页初始值
    
    while True:
        # 构造分页 URL
        url = "http://**********/operation-analytics/cluster/list/{}/200".format(toPage)
        print(url)
        
        # 发送 POST 请求获取数据
        response = requests.request("POST", url, headers=headers, data=payload)
        
        if response.status_code == 200:  # 如果请求成功
            total = response.json()["data"]["itemCount"]  # 获取总项数
            datas += response.json()["data"]["listObject"]  # 将返回的数据添加到 datas 中
            
            if len(datas) == total:  # 如果获取的数据已达到总数，结束循环
                break
            toPage += 1  # 否则继续请求下一页
        else:
            print(response.text)  # 打印错误信息
            break
    
    return datas  # 返回所有获取的数据

# 导入 CMDB 数据的函数
def cmdb_import(objectId,datas):
    url = url = "http://{}/object/{}/instance/_import".format(HOST,objectId)  # 构造导入接口 URL
    headers = {
        'org': str(EASYOPS_ORG),  # 设置组织信息
        'user': 'easyops',        # 设置用户信息
        'Content-Type': 'application/json',
        "host": "cmdb_resource.easyops-only.com"  # 设置请求头
    }
    params = {
        "keys": ["id"],  # 设置要使用的键
        "datas": datas    # 设置要导入的数据
    }
    
    # 发送 POST 请求将数据导入到 CMDB
    response = requests.request("POST", url, headers=headers, data=json.dumps(params))
    print(response.text)  # 打印返回结果

def import_batch(objectId,datas):
    for i in range(0, len(datas), 1000):
        ret = datas[i:i+1000]  # 获取当前批次的数据
        cmdb_import(objectId,ret)  # 调用导入函数

# 搜索 CMDB 中实例数据的函数
def cmdb_instance_search(objectId,params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST,objectId)  # 构造查询接口 URL
    headers = {
        'org': str(EASYOPS_ORG),  # 设置组织信息
        'user': 'easyops',        # 设置用户信息
        'Content-Type': 'application/json',
        "host": "cmdb_resource.easyops-only.com"  # 设置请求头
    }
    
    ret_list = []  # 存储查询结果
    page = 1  # 初始页码
    page_size = 200  # 每页大小
    
    while True:
        params["page"] = page  # 设置分页参数
        params["page_size"] = page_size
        
        # 发送请求获取数据
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        
        if ret["code"] == 0:  # 如果查询成功
            ret_list += ret["data"]["list"]  # 将数据添加到结果列表
            
            if len(ret["data"]["list"]) < page_size:  # 如果当前页数据不足 page_size，说明已是最后一页
                break
            page += 1  # 否则继续请求下一页
        else:
            print(ret)  # 打印错误信息
            break
    
    return ret_list  # 返回所有查询到的数据


# 批量审批 CMDB 数据的函数
def cmdb_approve(objectId,payload):
    url = "http://{}:8073/object/{}/instance/_import-json?source=interface&only_delete=true".format(HOST,objectId)  # 批量审批接口 URL

    payload = json.dumps(payload)  # 将请求体转换为 JSON 字符串
    headers = {
        'user': 'easyops',            # 设置用户信息
        'org': str(EASYOPS_ORG),      # 设置组织信息
        'content-type': 'application/json',
    }
    
    # 发送 POST 请求进行审批
    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text)  # 打印返回结果

def deal_cluster(access_key, signature):
    cluster_res = get_cluster_datas(access_key, signature)
    cluster_ids = []
    cmdb_cluster_ids = []
    for cluster in cluster_res:
        cluster["lastSyncTimestamp"] = datetime.fromtimestamp(cluster["lastSyncTimestamp"] / 1000).strftime('%Y-%m-%d %H:%M:%S')
        cluster_ids.append(cluster["id"])
    import_batch("CLOUD_CLUSTER",cluster_res)
    
    cluster_params = {"fields":{"zone":1,"id":1}}
    cluster_response = cmdb_instance_search("CLOUD_CLUSTER",params=cluster_params)
    cluster_dict = {}
    for cls in cluster_response:
        if cls.get("zone"):
            cluster_dict[cls["zone"]] = cls["instanceId"]
        if cls["id"] not in cluster_ids:
            cmdb_cluster_ids.append(cls["instanceId"])
    

    return cluster_dict,cmdb_cluster_ids
    
def deal_vm(access_key, signature,cluster_dict,host_dict):
    # 获取数据
    ret = get_datas(access_key, signature)
    
    datas = []  # 存储要导入的数据
    cloud_ids = []  # 存储云实例 ID
    
    for data in ret:
        # 格式化时间戳
        data["accountNamehost"] = data.get("accountName","") + "_" + data.get("host","")
        data["CLOUD_PLATFORM_WITH_CLOUD_HOST"] = {"instanceId":host_dict.get(data["accountNamehost"])}
        if data["createTime"]:
            data["createTime"] = datetime.fromtimestamp(data["createTime"] / 1000).strftime("%Y-%m-%d %H:%M:%S")
        if data["lastSyncTimestamp"]:
            data["lastSyncTimestamp"] = datetime.fromtimestamp(data["lastSyncTimestamp"] / 1000).strftime("%Y-%m-%d %H:%M:%S")
        if data["updateTime"]:
            data["updateTime"] = datetime.fromtimestamp(data["updateTime"] / 1000).strftime("%Y-%m-%d %H:%M:%S")
        
        data["_instanceId"] = data["instanceId"]  # 添加 _instanceId
        if data.get("zone"):
            data["cluster"] = {"instanceId":cluster_dict.get(data["zone"])}
        
        # 处理 IP 数组
        if data["ipArray"]:
            data["ipArray"] = eval(data["ipArray"])
        
        del data["instanceId"]  # 删除原始 instanceId
        
        cloud_ids.append(data["id"])  # 将 ID 添加到 cloud_ids 列表
        datas.append(data)  # 将数据添加到 datas 列表
    
    # 分批导入数据，每次导入 1000 条
    import_batch("CLOUD_PLATFORM",datas) # 导入 CMDB
    
    time.sleep(10)  # 等待 10 秒
    
    # 查询 CMDB 中的实例数据
    cmdb_colud = cmdb_instance_search("CLOUD_PLATFORM",params={})
    cmdb_instanceIds = []
    
    # 筛选出不在云实例列表中的 ID
    for i in cmdb_colud:
        if i["id"] not in cloud_ids:
            cmdb_instanceIds.append(i["instanceId"])
            
    return cmdb_instanceIds
    
def deal_host(access_key, signature,cluster_dict):
    # 获取数据
    ret = get_host_datas(access_key, signature)
    
    datas = []  # 存储要导入的数据
    cloud_ids = []  # 存储云实例 ID
    
    for data in ret:
        # 格式化时间戳

        if data["lastSyncTimestamp"]:
            data["lastSyncTimestamp"] = datetime.fromtimestamp(data["lastSyncTimestamp"] / 1000).strftime("%Y-%m-%d %H:%M:%S")
        # if data.get("zone"):
        #     data["cluster"] = {"instanceId":cluster_dict.get(data["zone"])}
        data["accountNamehostId"] = data.get("accountName","") + "_" + data.get("hostId","")
        # 处理 IP 数组
        
        cloud_ids.append(data["id"])  # 将 ID 添加到 cloud_ids 列表
        datas.append(data)  # 将数据添加到 datas 列表
    #print json.dumps(datas,indent=2,ensure_ascii=False)
    # 分批导入数据，每次导入 1000 条
    import_batch("CLOUD_HOST",datas) # 导入 CMDB
    
    time.sleep(10)  # 等待 10 秒
    
    # # 查询 CMDB 中的实例数据
    cmdb_colud = cmdb_instance_search("CLOUD_HOST",params={})

    host_dict = {}
    cmdb_instanceIds = []
    
    # 筛选出不在云实例列表中的 ID
    for i in cmdb_colud:
        host_dict[i["accountNamehostId"]] = i["instanceId"]
        if i["id"] not in cloud_ids:
            cmdb_instanceIds.append(i["instanceId"])
            
    return cmdb_instanceIds,host_dict
    
# 主程序入口
if __name__ == '__main__':
    access_key = "xxxx"  # 设置访问密钥
    secret_key = "xxxxx"  # 设置私密密钥
    signature = get_signature(access_key, secret_key)  # 获取签名

    cluster_dict,exprie_cluster_instance = deal_cluster(access_key, signature)

    exprie_host_instance,host_dict = deal_host(access_key, signature,cluster_dict)
    
    exprie_vm_instance = deal_vm(access_key, signature,cluster_dict,host_dict)
    
    
    # 创建审批 payload
    # 列表推导式本身没有 append 操作。它的作用是自动遍历并生成一个新列表，而不是像 for 循环那样手动调用 append
    # vm_payload = [{"filter": {"instanceId": instanceId}, "update": {}, "upsert": False} for instanceId in exprie_vm_instance]
    # 这里使用 for 循环来构造 vm_payload 列表，等价于上面的列表推导式
    vm_payload = []
    for instanceId in exprie_vm_instance:
        item = {
            "filter": {"instanceId": instanceId},
            "update": {},
            "upsert": False
        }
        vm_payload.append(item)

    # 批量审批
    cmdb_approve("CLOUD_PLATFORM",vm_payload)
    
    # host_payload = [{"filter": {"instanceId": instanceId}, "update": {}, "upsert": False} for instanceId in exprie_host_instance]
    # 这里使用 for 循环来构造 host_payload 列表，等价于上面的列表推导式
    host_payload = []
    for instanceId in exprie_host_instance:
        host_payload.append({
            "filter": {"instanceId": instanceId},
            "update": {},
            "upsert": False
        })    
    # 批量审批
    cmdb_approve("CLOUD_HOST",host_payload)
    
    # 创建审批 payload
    # cluster_payload = [{"filter": {"instanceId": instanceId}, "update": {}, "upsert": False} for instanceId in exprie_cluster_instance]
    # 这里使用 for 循环来构造 cluster_payload 列表，等价于上面的列表推导式
    cluster_payload = []
    for instanceId in exprie_cluster_instance:
        item = {
            "filter": {"instanceId": instanceId},
            "update": {},
            "upsert": False
        }
        cluster_payload.append(item)
    # 批量审批
    cmdb_approve("CLOUD_CLUSTER",cluster_payload)



"""
该脚本主要实现了从云管平台批量同步虚拟机、宿主机、集群等信息到 CMDB（配置管理数据库），并对过期数据进行批量审批删除。整体流程如下：
1. 签名生成
get_signature(access_key, secret_key)
用 AES-CBC 加密生成签名，签名内容包含 access_key、UUID 和当前时间戳，便于接口鉴权。
2. 数据获取
get_datas：获取云管平台虚拟机（VM）数据，分页拉取。
get_host_datas：获取宿主机（Host）数据，分页拉取。
get_cluster_datas：获取集群（Cluster）数据，分页拉取。
3. 数据导入 CMDB
cmdb_import：调用 CMDB 的 _import 接口导入数据。
import_batch：每 1000 条数据为一批，分批导入。
4. CMDB 查询与审批
cmdb_instance_search：分页查询 CMDB 某对象的所有实例。
cmdb_approve：批量审批（删除）CMDB 中的实例。
5. 数据处理主流程
deal_cluster：同步集群数据到 CMDB，返回集群字典和过期集群实例 ID。
deal_host：同步宿主机数据到 CMDB，返回宿主机字典和过期宿主机实例 ID。
deal_vm：同步虚拟机数据到 CMDB，返回过期虚拟机实例 ID。
6. 主程序入口
依次同步集群、宿主机、虚拟机数据到 CMDB。
查询 CMDB 中已不存在于云管平台的数据（即过期数据）。
构造批量审批 payload，调用 cmdb_approve 删除这些过期数据。
7. 关键点说明
分页拉取：所有数据接口都支持分页，避免一次性拉取过多数据。
时间戳格式化：将时间戳转为可读时间字符串。
数据唯一性：通过自定义字段（如 accountNamehostId）保证数据唯一性。
批量导入/审批：提升效率，减少接口调用次数。
安全性：接口调用均需签名认证。
"""    
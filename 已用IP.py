#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import requests
import json
HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'org': str(EASYOPS_ORG),
    'user': 'easyops',
    'Content-Type': 'application/json',
    "host": "cmdb_resource.easyops-only.com"
}

def cmdb_instance_search(object_id, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []  # 初始化返回列表，用于存储符合条件的数据
    page = 1  # 页码，初始为第一页
    page_size = 200  # 每页返回的数据条数
    while True:
        # 设置分页参数
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:  # 如果响应正常
            ret_list += ret["data"]["list"]  # 将当前页的数据添加到返回列表
            if len(ret["data"]["list"]) < page_size: # 如果返回的数据小于每页条数，说明已到达最后一页
                break
            # 否则，继续请求下一页
            page += 1
        else:
            print(ret) # 如果响应错误，打印错误信息并退出循环
            break
    return ret_list  # 返回所有符合条件的实例数据

def cmdb_import(objectId, datas):
    url = "http://{}/object/{}/instance/_import".format(HOST, objectId)
    payload = {
        "keys": ["used_ip"],
        "datas": datas
    }
    print(json.dumps(payload, ensure_ascii=False, indent=2))
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    print(json.dumps(json.loads(response.text), indent=4, ensure_ascii=False))



if __name__ == "__main__":
    # 定义需要查询的模型列表
    models_list = ["ROUTE@ONEMODEL", "SWITCH@ONEMODEL", "LOAD_BALANCE", "FIREWALL@ONEMODEL", "HOST"]

    # 存储所有IP数据
    all_ip_data = []

    # 遍历模型列表，查询每个模型的IP数据
    for model in models_list:
        # 查询参数，查找存在IP字段的实例
        params = {"fields": {"ip": True}, "query": {"ip": {"$exists":True}}}

        # 查询当前模型的实例
        print("正在查询模型: {}".format(model))
        instances = cmdb_instance_search(model, params)
        print("查询到 {} 个实例".format(len(instances)))

        # 处理当前模型的IP数据
        for instance in instances:
            used_ip = {}
            used_ip["used_ip"] = instance["ip"]
            all_ip_data.append(used_ip)

    # 打印汇总信息
    print("所有模型共查询到 {} 个IP".format(len(all_ip_data)))
    print(json.dumps(all_ip_data, ensure_ascii=False, indent=2))

    # 导入数据到USED_IP模型
    cmdb_import("USED_IP", all_ip_data)
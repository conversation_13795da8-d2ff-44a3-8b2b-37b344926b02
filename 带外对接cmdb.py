#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-
# python2中如果有中文字符，需要指定编码类型为uft8，否则会乱码

import requests
import json
import time
import os

# 带外系统主机IP
HOST_IP = "**********"
# EASYOPS_CMDB_HOST变量存储的是CMDB平台的ip:port；截取ip
HOST = EASYOPS_CMDB_HOST.split(":")[0]
# 定义请求头
headers = {
        'org': str(EASYOPS_ORG),  # 组织 ID
        'user': 'easyops',  # 用户名（用于认证）
        'Content-Type': 'application/json',  # 请求的内容类型为 JSON
        "host": "cmdb_resource.easyops-only.com"  # 目标主机地址
}

def fetch_device_info():
    url = "http://{}/ws/restful/dcm/asset/getdeviceinfo".format(HOST_IP)
    # 传参，token为必填项，否则会报错，其它看需要取哪些数据，就传哪些参数
    payload = {
        "token": "207428C29A56FB7F4E6B5B1E2F9D8F6B",
        # 查询条件，不能为空，至少一个
        "devicestatus": 1,   # 设备状态：0 待上线，1 已上线，2 已下线
        # "roomname": "西区备份中心",
        "showhardwareflag": "true",
        # "devicetype": 1
        "monitorstatus": 1
    }
    # 将python的字典转换为json格式数据
    response = requests.post(url, data=json.dumps(payload), headers={'Content-Type': 'application/json'}).json()["deviceList"]
    return response   # 不再执行函数体内return后面的逻辑

def cmdb_instance_search(objectId, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, objectId)
    ret_list = []  # 初始化返回列表，用于存储符合条件的数据
    page = 1  # 页码，初始为第一页
    page_size = 200  # 每页返回的数据条数
    
    while True:
        # 设置分页参数
        params["page"] = page
        params["page_size"] = page_size
        
        # 发送 POST 请求，获取实例数据
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        
        if ret["code"] == 0:  # 如果响应正常
            ret_list += ret["data"]["list"]  # 将当前页的数据添加到返回列表
            # 如果返回的数据小于每页条数，说明已到达最后一页
            if len(ret["data"]["list"]) < page_size:
                break
            # 否则，继续请求下一页
            page += 1
        else:
            # 如果响应错误，打印错误信息并退出循环
            print(ret)
            break
    # print ret_list        
    return ret_list  # 返回所有符合条件的实例数据

def cmdb_import(objectId,datas):
    url = "http://{}/object/{}/instance/_import".format(HOST,objectId)  # 构建导入CMDB的UR
    payload = {
        "keys": ["ip"],  # 主键字段（用于匹配）
        "datas": datas  # 要导入的数据
    }
    # 发送POST请求导入数据到CMDB
    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    # 打印返回的响应（格式化输出）
    # print(response.text)
    print "\ncmdb新增实例响应结果" + "." * 50    
    print json.dumps(json.loads(response.text), indent=4, ensure_ascii=False)
 
def main():
    ret = fetch_device_info()
    datas = []
    for i in ret:
        data = i.get("baseInfo")
        
        # 定义硬件信息字段映射，key为data中的字段，value为(一级key, 二级key)，如果二级key为None则直接赋整个对象
        hardware_fields = [
            ("cpu", "cpuInfo", "cpu"),
            ("cpuInfo", "cpuInfo", None),
            ("memory", "memoryInfo", "memory"),
            ("memoryInfo", "memoryInfo", None),
            ("disk", "diskInfo", "disk"),
            ("diskInfo", "diskInfo", None),
            ("power", "powerInfo", "power"),
            ("powerInfo", "powerInfo", None),
            ("port", "networkInfo", "port"),
            ("networkInfo", "networkInfo", None),
            ("raid", "raidInfo", "raid"),
            ("raidInfo", "raidInfo", None),
            ("pciecard", "pcieInfo", "pciecard"),
            ("pcieInfo", "pcieInfo", None),
            ("fan", "fanInfo", "fan"),
            ("fanInfo", "fanInfo", None),
            ("storagePool", "storagePoolInfo", "storagePool"),
            ("storageLun", "storageLunInfo", "storageLun"),
        ]
        for field, conf_key, sub_key in hardware_fields:
            conf = i["harewareConf"].get(conf_key)
            if conf:
                data[field] = conf[sub_key] if sub_key else conf

        # 网络相关字段
        network_fields = [
            ("oobnetwork", "oobnetwork"),
            ("productNetwork", "productNetwork"),
            ("hbaCardInfo", "hbaCardInfo"),
        ]
        for field, conf_key in network_fields:
            conf = i["networkConfig"].get(conf_key)
            if conf:
                data[field] = conf
                if field == "productNetwork":
                    productIP = conf[0]["ip"]

        # 采购与维保、管理信息
        if i.get("purchaseWarrantyInfo"):
            data["purchaseWarrantyInfo"] = i["purchaseWarrantyInfo"]
        if i.get("manageInfo"):
            data["manageInfo"] = i["manageInfo"]

        # fields：定义需要返回的字段
        params = {"fields":{"instanceId":True},"query":{"ip":{"$eq":productIP}}}
        # 查询出等于productIP的所有实例
        ret = cmdb_instance_search("PHYSICAL_SERVER@ONEMODEL", params=params)
        # 定义一个空列表
        relations = []
        for i in ret:
            # 定义一个空字典,将实例的 instanceId添加到relation字典中
            relation = {}
            relation["instanceId"] = i["instanceId"]
            # 将relation字典添加到relations列表中
            relations.append(relation)
            # 将relations列表赋值给data字典的"PHYSICAL_SERVER_WITH_IPMI_SYSTEM"键;
            # "PHYSICAL_SERVER_WITH_IPMI_SYSTEM"相当于一个字段id，和其它字段id一样
            data["PHYSICAL_SERVER_WITH_IPMI_SYSTEM"] = relations
        # 将data字典添加到datas列表中    
        datas.append(data)
        # print json.dumps(relations,indent=2,ensure_ascii=False) # indent=4，格式化输出
        # print productIP
    print "需要导入cmdb的实例总数:", len(datas)    
    # print json.dumps(datas,indent=2,ensure_ascii=False)        
    # 然后调用这个函数，将实例id导入cmdb    
    cmdb_import("IPMI_SYSTEM",datas)

if __name__ == "__main__":
    main()    
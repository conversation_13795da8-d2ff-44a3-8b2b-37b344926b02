#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    """
    通用CMDB实例查询函数，根据传入的模型ID和查询参数返回实例列表
    """
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list

# def update_instance_permissions(object_id, instance_ids, users, fields, method="overwrite"):
def update_instance_permissions(object_id, instance_ids, users, fields, method="append"):    
    """
    批量修改CMDB实例权限
    参数:
    object_id - 模型ID
    instance_ids - 实例ID列表
    users - 用户/用户组列表
    fields - 权限字段列表，如 updateAuthorizers, deleteAuthorizers
    method - 权限修改方法，可选值: append(追加), overwrite(替换覆盖，结合put可以批量清空), pull(移除)
    """
    url = "http://10.7.231.40:8079/permission/{}/instances/_batch".format(object_id)
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }

    print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)
    
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    return response


def main():
    lb_object_id = "LOAD_BALANCE"
    firewall_object_id = "FIREWALL@ONEMODEL"
    user_object_id = "USER"   
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    method = "append"

    # relation_id = "_ASSETS_LOAD_BLANCE"
    relation_id = "_ASSETS_FIREWALL"
    assetResponsiblePerson_params = {
        "fields": {
            "instanceId": True,
            relation_id: True
        },
        "query": {
            "$and": [
                {relation_id: {"$exists": True}}
            ]
        }
    }    

    # load_balance_instances = cmdb_instance_search(lb_object_id, assetResponsiblePerson_params)
    firewall_instances = cmdb_instance_search(firewall_object_id, assetResponsiblePerson_params)
    # print u"查询到的负载均衡实例总数:", len(load_balance_instances)
    print u"查询到的防火墙实例总数:", len(firewall_instances)
    print "-----------遍历列表-----------"

    # 这里用字典缓存 nickname->name，避免重复查询
    user_cache = {}
    # 计数器
    update_instance_permissions_count = 0

    # 负载均衡
    # for lb in load_balance_instances:
    #     instance_id = lb.get("instanceId")
    #     _ASSETS_LOAD_BLANCE = lb.get(relation_id, [])
    #     print "负载均衡模型实例id：", instance_id
    #     for asset in _ASSETS_LOAD_BLANCE:
    #         arp = asset.get("assetResponsiblePerson")
    #         if arp and arp != "None":
    #             print "资产责任人：" , arp
    #             if arp in user_cache:
    #                 name = user_cache[arp]
    #             else:
    #                 ret = cmdb_instance_search(
    #                     user_object_id,
    #                     params={
    #                         "fields": {"name": True},
    #                         "query": {"nickname": {"$eq": arp}}
    #                     }
    #                 )
    #                 print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
    #                 name = None
    #                 if ret:
    #                     name = ret[0].get("name")
    #                 user_cache[arp] = name
    #                 print "用户名缓存：", name
    #                 print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)

    #             if instance_id and name:
    #                 response = update_instance_permissions(
    #                     object_id=lb_object_id,
    #                     instance_ids=[instance_id],
    #                     users=[name],
    #                     fields=fields,
    #                     method=method
    #                 )
    #                 print "状态码:", response.status_code
    #                 print "响应内容:"
    #                 try:
    #                     print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    #                 except:
    #                     print(response.text)
    #                 update_instance_permissions_count += 1

    # 防火墙
    for firewall in firewall_instances:
        instance_id = firewall.get("instanceId")
        _ASSETS_FIREWALL = lb.get(relation_id, [])
        print "防火墙模型实例id：", instance_id
        for asset in _ASSETS_FIREWALL:
            arp = asset.get("assetResponsiblePerson")
            if arp and arp != "None":
                print "资产责任人：" , arp
                if arp in user_cache:
                    name = user_cache[arp]
                else:
                    ret = cmdb_instance_search(
                        user_object_id,
                        params={
                            "fields": {"name": True},
                            "query": {"nickname": {"$eq": arp}}
                        }
                    )
                    print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
                    name = None
                    if ret:
                        name = ret[0].get("name")
                    user_cache[arp] = name
                    print "用户名缓存：", name
                    print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)

                if instance_id and name:
                    response = update_instance_permissions(
                        object_id=lb_object_id,
                        instance_ids=[instance_id],
                        users=[name],
                        fields=fields,
                        method=method
                    )
                    print "状态码:", response.status_code
                    print "响应内容:"
                    try:
                        print(json.dumps(response.json(), indent=2, ensure_ascii=False))
                    except:
                        print(response.text)
                    update_instance_permissions_count += 1                        
    print "要设置的白名单总实例数：", update_instance_permissions_count


if __name__ == "__main__":
    main()
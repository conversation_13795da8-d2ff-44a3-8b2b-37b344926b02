import requests
import json
import zipfile
import io
import sys
from requests.auth import HTTPBasicAuth
import warnings

reload(sys)
sys.setdefaultencoding("utf8")
warnings.filterwarnings("ignore")
HOST = EASYOPS_CMDB_HOST.split(":")[0]


# 获取 json 压缩包和bcf 文件token
def get_file_token(method, ip):
    url = "https://{}/api/lb/current-version/sys/backup-config-package".format(ip)
    
    if method == "GET":
        payload = {}
        
    elif method == "POST":
        payload = json.dumps({"type": "JSON-CONFIG-PACKAGE"})
    
    try:
        response = requests.request(method, url, headers=headers, data=payload, verify=False).text
        ret = json.loads(response)
        file_token = ret["file_token"]
        
        return file_token
    except Exception as e:
        print("获取文件token失败: {0}".format(e))
    print(file_token)
    

# 下载保存对应的文件
def export_file(ip):
    bcf_file_token = get_file_token("GET", ip)
    json_file_token = get_file_token("POST", ip)
    payload = {}
    # 下载json zip 压缩包并解压到对应的ip目录
    try:
        url = "https://*************/cgi/file-resource?d={0}".format(json_file_token)
        response = requests.request("GET", url, headers=headers, data=payload, verify=False)
        if response.status_code == 200:
            # 将压缩包读取到内存
            zip_file = zipfile.ZipFile(io.BytesIO(response.content))
             # 指定解压路径
            extract_path = os.path.join(backup_dir,"负载均衡", ip)

            if not os.path.exists(extract_path):
                os.makedirs(extract_path)
                # 解压压缩包
                zip_file.extractall(extract_path)
                print("{0} json 文件解压完成.".format(ip))
            else:
                 # 解压压缩包
                zip_file.extractall(extract_path)
                print("{0} json 文件解压完成.".format(ip))
    except Exception as e:
        print("{0} 获取json 压缩包失败，解压失败. {1}".format(ip, e))
    
    
    # 下载bcf文件
    try:
        url = "https://*************/cgi/file-resource?d={0}".format(bcf_file_token)
        response = requests.request("GET", url, headers=headers, data=payload, verify=False)
        # print(response.content)
        if response.status_code == 200:
            extract_path = os.path.join(backup_dir,"负载均衡/")
            with open("{0}{1}.bcf".format(extract_path, ip), "wb") as f:
                 # 以 1024 字节块写入
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            print("{0}{1}.bcf 保存成功".format(extract_path, ip))

    except Exception as e:
        print("{0} 获取bcf文件失败，解压失败: {1}".format(ip, e))
        

if __name__ == "__main__":
    headers = {
        'Authorization': 'Basic aXRtYW5hZ2VyOk1hbmFnZXJeNTU4NQ=='
    }
    backup_dir = "/data/net_device_backups/"
    ip = "*************"
    export_file(ip)
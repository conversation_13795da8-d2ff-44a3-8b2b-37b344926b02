#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import MySQLdb
import requests
import json

# 重新加载 sys 并设置默认编码为 UTF-8（适用于 Python 2）
reload(sys)
sys.setdefaultencoding("utf-8")

# 设备类型映射到 CMDB 的对象类型
model_map = {
    "switches": "SWITCH@ONEMODEL",
    "firewall": "FIREWALL@ONEMODEL",
    "sangfor": "LOAD_BALANCE",
    "f5": "LOAD_BALANCE",
    "router": "ROUTE@ONEMODEL"
}

# 数据库连接信息
IP = "*********"
PORT = 2883
USER = "ityw_read@ythjk#ob2"
PASSWORD = "CCsc@#ityw09"
DB = "simo_dev"

# CMDB 主机地址
HOST = EASYOPS_CMDB_HOST.split(":")[0]

def ob_connect():
    """
    连接数据库并查询网络设备数据
    :return: 查询结果列表
    """
    sql = """
        SELECT 
        ne_class, name, vendor_id, ip, model, asset_id, version, description, id
        FROM simo_dev.simo_monitor_network_entity
        WHERE manage_status != "Delected"
    """
    try:
        # 建立数据库连接
        connection = MySQLdb.connect(
            host=IP,
            port=PORT,
            user=USER,
            passwd=PASSWORD,
            db=DB
        )
        # 创建游标对象
        cursor = connection.cursor()
        cursor.execute(sql)
        # 获取查询结果
        result = cursor.fetchall()
        print("获取数据共 {0} 条".format(len(result)))
        return result
    except Exception as e:
        print("数据库操作出错: %s" % e)
    finally:
        # 关闭游标和连接
        cursor.close()
        connection.close()


def parser_ob_data(ob_data):
    """
    解析数据库查询结果并分类存储到对应设备类型的列表中
    :param ob_data: 数据库查询结果
    """
    for i in ob_data:
        device_info = {
            "dev_name": i[1],
            "brand": i[2],
            "ip": i[3],
            "model": i[4],
            # "propertyid": i[5],
            "version": i[6],
            "memo": i[7],
            "dev_id": i[8]
        }
        if i[0] == "switches":
            switch_data.append(device_info)
        elif i[0] == "router":
            route_data.append(device_info)
        elif i[0] == "firewall":
            firewall_data.append(device_info)
        elif i[0] == "f5" or i[0] == "sangfor":
            loadbalance_data.append(device_info)


def cmdb_import(device_type, datas):
    """
    将设备数据导入 CMDB
    :param device_type: 设备类型（CMDB 对象类型）
    :param datas: 需要导入的设备数据
    """
    url = "http://{0}/object/{1}/instance/_import".format(HOST, device_type)
    headers = {
        "user": "easyops",
        "org": str(EASYOPS_ORG),
        "host": "cmdb_resource.easyops-only.com",
        "content-type": "application/json"
    }
    payload = {
        "keys": ["ip"],  # 以 IP 作为唯一标识
        "datas": datas
    }
    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    print(response.text)
    
    # 检查是否有ip重复数据，堆叠设备
    if "重复数据" in response.text:
        device_instanceid = {}
        device_datas = []
        device_data = cmdb_instance_search(device_type)
        for i in device_data:
            device_instanceid[i.get("ip")] = i["instanceId"]
        data = json.loads(response.text)["data"]["data"][0]["data"]
        for i in data:
            i["instanceId"] = device_instanceid.get(i["ip"])
            device_datas.append(i)
        cmdb_import_instanceId(device_type, device_datas)
        

def cmdb_import_instanceId(device_type, datas):
    """
    将设备数据导入 CMDB
    :param device_type: 设备类型（CMDB 对象类型）
    :param datas: 需要导入的设备数据
    """
    url = "http://{0}/object/{1}/instance/_import".format(HOST, device_type)
    headers = {
        "user": "easyops",
        "org": str(EASYOPS_ORG),
        "host": "cmdb_resource.easyops-only.com",
        "content-type": "application/json"
    }
    payload = {
        "keys": ["instanceId"],  # 以 instanceId 作为唯一标识
        "datas": datas
    }
    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    print(response.text)

   
        
def cmdb_instance_search(device_type, params={}):
    url = "http://{0}/object/{1}/instance/_search".format(HOST, device_type)
    headers = {
      'org': str(EASYOPS_ORG),
      'user': 'easyops',
      'Content-Type': 'application/json',
      "host":"cmdb_resource.easyops-only.com"
    }
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url,headers=headers,data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1 
        else:
            print ret
            break
    return ret_list


if __name__ == "__main__":
    """
    主流程:
    1. 连接数据库获取设备数据
    2. 解析设备数据并分类
    3. 按设备类型批量导入 CMDB
    """
    switch_data = []
    route_data = []
    firewall_data = []
    loadbalance_data = []
    
    # 获取数据库中的设备数据
    ob_data = ob_connect()
    parser_ob_data(ob_data)

    # 按类型导入 CMDB，每 100 条为一批
    if switch_data:
        for i in range(0, len(switch_data), 100):
            cmdb_import("SWITCH@ONEMODEL", switch_data[i:i+100])
    
    if route_data:
        for i in range(0, len(route_data), 100):
            cmdb_import("ROUTE@ONEMODEL", route_data[i:i+100])
        
    if firewall_data:
        for i in range(0, len(firewall_data), 100):
            cmdb_import("FIREWALL@ONEMODEL", firewall_data[i:i+100])    
    
    if loadbalance_data:
        for i in range(0, len(loadbalance_data), 100):
            cmdb_import("LOAD_BALANCE", loadbalance_data[i:i+100])
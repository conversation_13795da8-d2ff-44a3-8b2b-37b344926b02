#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json
import time

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

# 兼容 Python2处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    """
    通用CMDB实例查询函数，根据传入的模型ID和查询参数返回实例列表
    """
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list


def update_instance_permissions(object_id, instance_ids, users, fields, method=""):
    """
    批量修改CMDB实例权限
    参数:
    object_id - 模型ID
    instance_ids - 实例ID列表
    users - 用户/用户组列表
    fields - 权限字段列表，如 updateAuthorizers, deleteAuthorizers
    method - 权限修改方法，可选值: append(追加), overwrite(替换覆盖，结合put可以批量清空), pull(移除)
    """
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }

    print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)

    response = requests.put(url, headers=headers, data=json.dumps(payload))
    return response


def process_device(device_name, object_id, relation_id, user_object_id, fields, method, user_cache, update_instance_permissions_count, batch_delay):
    print u"开始处理%s实例..." % device_name
    assetResponsiblePerson_params = {
        "fields": {
            "instanceId": True,
            relation_id: True
        },
        "query": {
            "$and": [
                {relation_id: {"$exists": True}}
            ]
        }
    }
    query_start_time = time.time()
    instances = cmdb_instance_search(object_id, assetResponsiblePerson_params)
    query_end_time = time.time()
    query_time = query_end_time - query_start_time
    print u"查询到的%s实例总数:" % device_name, len(instances)

    process_start_time = time.time()
    instance_data_list = []
    for instance in instances:
        instance_id = instance.get("instanceId")
        assets = instance.get(relation_id, [])
        print "%s模型实例id：" % device_name, instance_id
        for asset in assets:
            arp = asset.get("assetResponsiblePerson")
            if arp and arp != "None":
                print "资产责任人：", arp
                if arp in user_cache:
                    name = user_cache[arp]
                else:
                    ret = cmdb_instance_search(
                        user_object_id,
                        params={
                            "fields": {"name": True},
                            "query": {"nickname": {"$eq": arp}}
                        }
                    )
                    print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
                    name = None
                    if ret:
                        name = ret[0].get("name")
                    user_cache[arp] = name
                    print "用户名缓存：", name
                    print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)

                if instance_id and name:
                    instance_data_list.append({"instance_id": instance_id, "name": name})
                    update_instance_permissions_count += 1

    batch_count = 0
    batch = 300
    for i in range(0, len(instance_data_list), batch):
        batch_data = instance_data_list[i:i + batch]
        batch_count += 1
        print u"处理第 %d 批次，共 %d 个实例" % (batch_count, len(batch_data))

        for item in batch_data:
            response = update_instance_permissions(
                object_id=object_id,
                instance_ids=[item["instance_id"]],
                users=[item["name"]],
                fields=fields,
                method=method
            )
            print "状态码:", response.status_code
            print "响应内容:"
            try:
                print(json.dumps(response.json(), indent=2, ensure_ascii=False))
            except:
                print(response.text)

        if i + batch < len(instance_data_list):
            print u"批次处理完成，休息 %d 秒后继续下一批次..." % batch_delay
            time.sleep(batch_delay)

    process_end_time = time.time()
    process_time = process_end_time - process_start_time
    return query_time, process_time, update_instance_permissions_count


def main():
    # 记录整个脚本开始时间
    script_start_time = time.time()

    # 批次间延迟时间（秒）
    batch_delay = 1  # 每批次之间延迟1秒，可以根据需要调整

    user_object_id = "USER"
    lb_object_id = "LOAD_BALANCE"  # 负载均衡
    firewall_object_id = "FIREWALL@ONEMODEL"  # 防火墙
    switch_object_id = "SWITCH@ONEMODEL"  # 交换机
    route_object_id = "ROUTE@ONEMODEL"  # 路由器

    fields = ["updateAuthorizers", "deleteAuthorizers"]
    method = "overwrite"  # 重设权限，覆盖老权限；结合put方法可以实现清空权限，list为为空即可

    # 全局控制开关，控制是否处理对应设备类型
    process_load_balancer = False
    process_firewall = False
    process_switch = False
    process_router = True

    user_cache = {}
    update_instance_permissions_count = 0
    total_query_time = 0
    total_process_time = 0

    if process_load_balancer:
        q_time, p_time, count = process_device(
            "负载均衡", lb_object_id, "_ASSETS_LOAD_BLANCE", user_object_id,
            fields, method, user_cache, update_instance_permissions_count, batch_delay)
        total_query_time += q_time
        total_process_time += p_time
        update_instance_permissions_count = count

    if process_firewall:
        q_time, p_time, count = process_device(
            "防火墙", firewall_object_id, "_ASSETS_FIREWALL", user_object_id,
            fields, method, user_cache, update_instance_permissions_count, batch_delay)
        total_query_time += q_time
        total_process_time += p_time
        update_instance_permissions_count = count

    if process_switch:
        q_time, p_time, count = process_device(
            "交换机", switch_object_id, "_ASSETS_SWITCH", user_object_id,
            fields, method, user_cache, update_instance_permissions_count, batch_delay)
        total_query_time += q_time
        total_process_time += p_time
        update_instance_permissions_count = count

    if process_router:
        q_time, p_time, count = process_device(
            "路由器", route_object_id, "_ASSETS_ROUTE", user_object_id,
            fields, method, user_cache, update_instance_permissions_count, batch_delay)
        total_query_time += q_time
        total_process_time += p_time
        update_instance_permissions_count = count

    print "要设置的白名单总实例数：", update_instance_permissions_count

    # 计算并输出总耗时
    script_end_time = time.time()
    total_time = script_end_time - script_start_time

    print u"\n========== 脚本执行时间统计 =========="
    print u"1. 查询实例耗时: %.2f 秒" % total_query_time
    print u"2. 设置实例白名单耗时: %.2f 秒" % total_process_time
    print u"3. 执行总耗时: %.2f 秒 (%.2f 分钟)" % (total_time, total_time / 60)


if __name__ == "__main__":
    main()
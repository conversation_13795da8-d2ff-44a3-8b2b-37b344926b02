#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json
import time

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list

def update_instance_permissions(object_id, instance_ids, users, fields, method=""):
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }
    print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    return response

def process_device(device_name, object_id, relation_id, user_object_id, fields, method, user_cache, batch_delay):
    print u"开始处理%s实例..." % device_name
    params = {
        "fields": {
            "instanceId": True,
            relation_id: True
        },
        "query": {
            "$and": [
                {relation_id: {"$exists": True}}
            ]
        }
    }
    query_start_time = time.time()
    instances = cmdb_instance_search(object_id, params)
    query_time = time.time() - query_start_time
    print u"查询到的%s实例总数:" % device_name, len(instances)

    instance_data_list = []
    for instance in instances:
        instance_id = instance.get("instanceId")
        assets = instance.get(relation_id, [])
        print "%s模型实例id：" % device_name, instance_id
        for asset in assets:
            arp = asset.get("assetResponsiblePerson")
            if arp and arp != "None":
                print "资产责任人：", arp
                name = user_cache.get(arp)
                if not name:
                    ret = cmdb_instance_search(
                        user_object_id,
                        params={
                            "fields": {"name": True},
                            "query": {"nickname": {"$eq": arp}}
                        }
                    )
                    print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
                    name = ret[0].get("name") if ret else None
                    user_cache[arp] = name
                    print "用户名缓存：", name
                    print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)
                if instance_id and name:
                    instance_data_list.append({"instance_id": instance_id, "name": name})

    process_start_time = time.time()
    batch = 300
    update_count = 0
    for batch_count, i in enumerate(range(0, len(instance_data_list), batch), 1):
        batch_data = instance_data_list[i:i + batch]
        print u"处理第 %d 批次，共 %d 个实例" % (batch_count, len(batch_data))
        for item in batch_data:
            response = update_instance_permissions(
                object_id=object_id,
                instance_ids=[item["instance_id"]],
                users=[item["name"]],
                fields=fields,
                method=method
            )
            update_count += 1
            print "状态码:", response.status_code
            print "响应内容:"
            try:
                print(json.dumps(response.json(), indent=2, ensure_ascii=False))
            except:
                print(response.text)
        if i + batch < len(instance_data_list):
            print u"批次处理完成，休息 %d 秒后继续下一批次..." % batch_delay
            time.sleep(batch_delay)
    process_time = time.time() - process_start_time
    return query_time, process_time, update_count

def main():
    script_start_time = time.time()
    batch_delay = 1

    user_object_id = "USER"
    devices = [
        {"name": "负载均衡", "object_id": "LOAD_BALANCE", "relation_id": "_ASSETS_LOAD_BLANCE", "enable": False},
        {"name": "防火墙", "object_id": "FIREWALL@ONEMODEL", "relation_id": "_ASSETS_FIREWALL", "enable": False},
        {"name": "交换机", "object_id": "SWITCH@ONEMODEL", "relation_id": "_ASSETS_SWITCH", "enable": False},
        {"name": "路由器", "object_id": "ROUTE@ONEMODEL", "relation_id": "_ASSETS_ROUTE", "enable": True},
    ]

    fields = ["updateAuthorizers", "deleteAuthorizers"]
    method = "overwrite"

    user_cache = {}
    total_update_count = 0
    total_query_time = 0
    total_process_time = 0
    
    # 用于存储每种设备类型的实例数量
    device_counts = {}

    for device in devices:
        if device["enable"]:
            q_time, p_time, count = process_device(
                device["name"], device["object_id"], device["relation_id"],
                user_object_id, fields, method, user_cache, batch_delay
            )
            total_query_time += q_time
            total_process_time += p_time
            device_counts[device["name"]] = count
            total_update_count += count

    # 打印每种设备类型的实例数量
    print u"\n========== 白名单实例数量统计 =========="
    for device_name, count in device_counts.items():
        print u"%s白名单实例数量: %d" % (device_name, count)
    
    # 打印总实例数量
    print u"白名单总实例数: %d" % total_update_count

    total_time = time.time() - script_start_time
    print u"\n========== 脚本执行时间统计 =========="
    print u"1. 查询实例耗时: %.2f 秒" % total_query_time
    print u"2. 设置实例白名单耗时: %.2f 秒" % total_process_time
    print u"3. 执行总耗时: %.2f 秒 (%.2f 分钟)" % (total_time, total_time / 60)

if __name__ == "__main__":
    main()
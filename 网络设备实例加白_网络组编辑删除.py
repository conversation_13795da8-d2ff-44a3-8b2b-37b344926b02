#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json


HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list

def update_instance_permissions(object_id, instance_ids, groups, fields, method=""):
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    payload = {
        "method": method,
        "list": groups,
        "fields": fields,
        "ids": instance_ids
    }
    print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    return response

def main():
    method = "overwrite"
    groups = ["信息技术总部网络安全组"]
    fields = ["updateAuthorizers", "deleteAuthorizers"]

    devices = [
        {"name": "负载均衡", "object_id": "LOAD_BALANCE", "enable": True},
        {"name": "防火墙", "object_id": "FIREWALL@ONEMODEL", "enable": True},
        {"name": "交换机", "object_id": "SWITCH@ONEMODEL", "enable": True},
        {"name": "路由器", "object_id": "ROUTE@ONEMODEL","enable": True},
    ] 

    instance_ids = []
    for device in devices:
        if device["enable"]:
            object_id = device["object_id"]
            print "正在处理设备:", device["name"]
            params = {"fields": {"instanceId": True}}
            instances = cmdb_instance_search(object_id, params)
            for instance in instances:
                instance_id = instance.get("instanceId")
                if instance_id:
                    instance_ids.append(instance_id)
            ret = update_instance_permissions(
                object_id=object_id,
                instance_ids=instance_ids,
                groups=groups,
                fields=fields,
                method=method
            )
            
            # print json.dumps(instance_ids, ensure_ascii=False, indent=2)
            print len(instance_ids)
            # instance_ids = []
            # print len(instance_ids)
            print "状态码:", ret.status_code
            print "响应内容:"
            try:
                print(json.dumps(ret.json(), indent=2, ensure_ascii=False))
            except:
                print(ret.text)
            instance_ids = []
            print len(instance_ids)            


if __name__ == "__main__":
    main()
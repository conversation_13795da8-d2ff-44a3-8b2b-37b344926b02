#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-
import sys
import requests
import json
from datetime import datetime
from elasticsearch import Elasticsearch

reload(sys)
sys.setdefaultencoding("utf-8")

HOST = EASYOPS_CMDB_HOST.split(":")[0]

# 网络设备端口字段对照表
prot_fileds_map = {
    "if_admin_status":"adminStatus",
    "if_descr":"ifName",
    "if_oper_status":"operStatus",
    "if_out_speed":"speed",
    "if_type":"type",
    "if_phys_address":"physAddress"
}
adminStatus_enum_map = {
    "1":"up",
    "2":"down",
    "3":"testing"
}
operStatus_enum_map = {
    "1":"up",
    "2":"down",
    "3":"testing",
    "4":"unknown",
    "5":"dormant",
    "6":"notPresent",
    "7":"lowerLayerDown"
}
type_enum_map = {"1": "other","2": "regular1822","3": "hdh1822","4": "ddn-x25","5": "x25","6": "ethernet-csmacd","7": "IEEE802.3","8": "IEEE802.4","9": "IEEE802.5","10": "iso88026-man","11": "starLan","12": "proteon-10Mbit","13": "proteon-80Mbit","14": "hyperchannel","15": "FDDI","16": "lapb","17": "sdlc","18": "ds1","19": "e1","20": "basicISDN","21": "primaryISDN","22": "propPointToPointSerial","23": "ppp","24": "softwareLoopback","25": "eon","26": "ethernet-3Mbit","27": "nsip","28": "slip","29": "ultra","30": "ds3","31": "sip","32": "frame-relay","33": "RS-232","34": "Parallel","35": "arcnet","36": "arcnet-plus","37": "atm","38": "MIOX25","39": "SONET","40": "x25ple","41": "iso88022llc","42": "localTalk","43": "smds-dxi","44": "frameRelayService","45": "v35","46": "hssi","47": "hippi","48": "modem","49": "aal5","50": "sonetPath","51": "sonetVT","52": "smds-icip","53": "propVirtual","54": "propMultiLink","55": "ieee80212","56": "fibre-channel","57": "hippiInterfaces","58": "FrameRelayInterconnect","59": "aflane8023","60": "aflane8025","61": "cctEmul","62": "fastEther","63": "isdn","64": "v11","65": "v36","66": "g703-64k","67": "g703-2mb","68": "qllc","69": "fastEtherFX","70": "channel","71": "IEEE802.11","72": "ibm370parChan","73": "ESCON","74": "DLSw","75": "ISDNs","76": "ISDNu","77": "lapd","78": "ip-switch","79": "rsrb","80": "atm-logical","81": "ds0","82": "ds0Bundle","83": "bsc","84": "async","85": "cnr","86": "iso88025Dtr","87": "eplrs","88": "arap","89": "propCnls","90": "hostPad","91": "termPad","92": "frameRelayMPI","93": "x213","94": "adsl","95": "radsl","96": "sdsl","97": "vdsl","98": "iso88025CRFPInt","99": "myrinet","100": "voiceEM","101": "voiceFXO","102": "voiceFXS","103": "voiceEncap","104": "voiceOverIp","105": "atmDxi","106": "atmFuni","107": "atmIma","108": "pppMultilinkBundle","109": "ipOverCdlc","110": "ipOverClaw","111": "stackToStack","112": "virtualIpAddress","113": "mpc","114": "ipOverAtm","115": "iso88025Fiber","116": "tdlc","117": "gigabitEthernet","118": "hdlc","119": "lapf","120": "v37","121": "x25mlp","122": "x25huntGroup","123": "transpHdlc","124": "interleave","125": "fast","126": "ip","127": "docsCableMaclayer","128": "docsCableDownstream","129": "docsCableUpstream","130": "a12MppSwitch","131": "tunnel","132": "coffee","133": "ces","134": "atmSubInterface","135": "l2vlan","136": "l3ipvlan","137": "l3ipxvlan","138": "digitalPowerLine","139": "mediaMailOverIp","140": "dtm","141": "dcn","142": "ipForward","143": "msdsl","144": "ieee1394     IEEE1394","145": "if-gsn","146": "dvbRccMacLayer","147": "dvbRccDownstream","148": "dvbRccUpstream","149": "atmVirtual","150": "mplsTunnel","151": "srp","152": "voiceOverAtm","153": "voiceOverFrameRelay","154": "idsl","155": "compositeLink","156": "ss7SigLink","157": "propWirelessP2P","158": "frForward","159": "rfc1483","160": "USB","161": "ieee8023adLag","162": "bgpPolicyAccounting","163": "frf16MfrBundle","164": "h323Gatekeeper","165": "h323Proxy","166": "mpls","167": "mfSigLink","168": "hdsl2","169": "shdsl","170": "ds1FDL","171": "POS","172": "dvbAsiIn","173": "dvbAsiOut","174": "plc","175": "NFAS","176": "TR008","177": "GR303RDT","178": "GR303IDT","179": "ISUP","180": "propDocsWirelessMaclayer","181": "propDocsWirelessDownstream","182": "propDocsWirelessUpstream","183": "hiperlan2","184": "propBWAp2Mp","185": "sonetOverheadChannel","186": "digitalWrapperOverheadChannel","187": "aal2","188": "radioMAC","189": "atmRadio","190": "IMT","191": "mvl","192": "reachDSL","193": "frDlciEndPt","194": "atmVciEndPt","195": "opticalChannel","196": "opticalTransport","197": "propAtm","198": "voiceOverCable","199": "infiniband","200": "teLink","201": "q2931","202": "virtualTg","203": "sipTg","204": "sipSig","205": "docsCableUpstreamChannel","206": "econet","207": "pon155","208": "pon622","209": "bridge","210": "linegroup","211": "voiceEMFGD","212": "voiceFGDEANA","213": "voiceDID","214": "mpegTransport","215": "sixToFour","216": "gtp","217": "pdnEtherLoop1","218": "pdnEtherLoop2","219": "opticalChannelGroup","220": "homepna","221": "gfp","222": "ciscoISLvlan","223": "actelisMetaLOOP","224": "fcipLink","225": "rpr","226": "qam","227": "lmp","228": "cblVectaStar","229": "docsCableMCmtsDownstream","230": "adsl2","231": "macSecControlledIF","232": "macSecUncontrolledIF","233": "aviciOpticalEther","234": "atmbond","235": "voiceFGDOS","236": "mocaVersion1","237": "ieee80216WMAN","238": "adsl2plus","239": "dvbRcsMacLayer","240": "dvbTdm","241": "dvbRcsTdma","242": "x86Laps","243": "wwanPP","244": "wwanPP2","245": "voiceEBS","246": "ifPwType","247": "ILAN","248": "PIP","249": "aluELP","250": "gpon","251": "vdsl2","252": "capwapDot11Profile","253": "capwapDot11Bss","254": "capwapWtpVirtualRadio","255": "bits","256": "docsCableUpstreamRfPort","257": "cableDownstreamRfPort","258": "vmwareVirtualNic","259": "ieee802154","260": "otnOdu","261": "otnOtu","262": "ifVfiType","263": "g9981","264": "g9982","265": "g9983","266": "aluEpon(E-PON)","267": "aluEponOnu","268": "aluEponPhysicalUni","269": "aluEponLogicalLink","270": "aluGponOnu","271": "aluGponPhysicalUni","272": "vmwareNicTeam","273": "Reserved","274": "Reserved","275": "Reserved","276": "Reserved","277": "docsOfdmDownstream","278": "docsOfdmaUpstream","279": "gfast","280": "sdci","281": "xboxWireless","282": "fastdsl","283": "docsCableScte55d1FwdOob","284": "docsCableScte55d1RetOob","285": "docsCableScte55d2DsOob","286": "docsCableScte55d2UsOob","287": "docsCableNdf","288": "docsCableNdr","289": "ptm","290": "ghn","291": "otnOtsi","292": "otnOtuc","293": "otnOduc","294": "otnOtsig","295": "microwaveCarrierTermination","296": "microwaveRadioLinkTerminal","297": "ieee8021axDrni","298": "ax25"}

def timestamp_to_localtime(timestamp):
    dt_local = datetime.fromtimestamp(int(timestamp) / 1000)  # 转换为本地时间
    localtime = dt_local.strftime("%Y-%m-%d %H:%M:%S")
    return localtime
    

def cmdb_instance_search(device_type, params={}):
    url = "http://{0}/object/{1}/instance/_search".format(HOST, device_type)
    headers = {
      'org': str(EASYOPS_ORG),
      'user': 'easyops',
      'Content-Type': 'application/json',
      "host":"cmdb_resource.easyops-only.com"
    }
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url,headers=headers,data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1 
        else:
            print ret
            break
    return ret_list


def connect_es():
    # 连接 ES，修改为你的 ES 地址、端口
    try:
        es = Elasticsearch(
            [ip],  # ES 地址
            verify_certs=False  # 如果使用 HTTPS 并有自签名证书，设置为 False
        )
        return es
    except Exception as e:
        print("ES连接失败: {0}".format(e))


def query_es():
    es = connect_es()
    page_size = 30  # 每页 10 条
    page = 0  # 第 1 页
    es_data = []

    while True:
        query={
            "_source": ["_id","ne","tm"],
            "query": {
                "match_all": {}
            },
            "from": page * page_size,  # 计算偏移量
            "size": page_size
        }
        query["_source"].extend(["v."+ i for i in prot_fileds_map.keys()])
        # print(query)

        res = es.search(index=index, body=query)
        hits = res.get("hits", {}).get("hits", [])
        if not hits:
            break  # 没有更多数据，结束循环
        es_data.extend(hits)
        page += 1
    
    return es_data
    

def parser_es_data(es_data):
    for data in es_data:
        monitor_device_id = data["_source"]["ne"]
        for v in data["_source"]["v"]:
            port_data = {}
            for key, value in v.items():
                if key in prot_fileds_map.keys():
                    if key == "if_admin_status":
                        if value in adminStatus_enum_map.keys():
                            port_data[prot_fileds_map[key]] = adminStatus_enum_map[value]
                        else:
                            port_data[prot_fileds_map[key]] = value
                            
                    elif key == "if_oper_status":
                        if value in operStatus_enum_map.keys():
                            port_data[prot_fileds_map[key]] = operStatus_enum_map[value]
                        else:
                            port_data[prot_fileds_map[key]] = value
                            
                    elif key == "if_type":
                        if value in type_enum_map.keys():
                            port_data[prot_fileds_map[key]] = type_enum_map[value]
                        else:
                            port_data[prot_fileds_map[key]] = value
                            
                    else:
                        port_data[prot_fileds_map[key]] = value
                    
            port_data["cDataUptime"] = timestamp_to_localtime(data["_source"]["tm"])

            if monitor_device_id in monitor_id.keys():
                port_name = v.get("if_descr")
                if port_name:
                    port_data["name"] = monitor_id[monitor_device_id][1] + ":" + port_name
                    
                    if monitor_id[monitor_device_id][0] == "SWITCH@ONEMODEL":
                        port_data["SWITCH"] = monitor_id[monitor_device_id][2]
                    elif monitor_id[monitor_device_id][0] == "ROUTE@ONEMODEL":
                        port_data["ROUTE"] = monitor_id[monitor_device_id][2]
                    elif monitor_id[monitor_device_id][0] == "FIREWALL@ONEMODEL":
                        port_data["FIREWALL"] = monitor_id[monitor_device_id][2]
                    elif monitor_id[monitor_device_id][0] == "LOAD_BALANCE":
                        port_data["LOAD_BALANCE"] = monitor_id[monitor_device_id][2]
                
                    port_datas.append(port_data)
            
            
def cmdb_import(datas):
    url = "http://{}/object/NETDPORT@ONEMODEL/instance/_import".format(HOST)
    headers = {"user":"easyops","org":"2024021401","host":"cmdb_resource.easyops-only.com","content-type":"application/json"}
    payload = {
        "keys":["name"],
        "datas":datas
        }

    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    print response.text
    

if __name__ == "__main__":
    index = "monitor_current_network_if_entry"
    ip = "*********:9210"
    port_datas = []
    monitor_id = {}
    
    device_data = cmdb_instance_search("SWITCH@ONEMODEL")
    for device in device_data:
        if "dev_id" in device.keys():
            monitor_id[device["dev_id"]] = ["SWITCH@ONEMODEL", device["ip"], device["instanceId"]]
            
    device_data = cmdb_instance_search("ROUTE@ONEMODEL")
    for device in device_data:
        if "dev_id" in device.keys():
          monitor_id[device["dev_id"]] = ["ROUTE@ONEMODEL", device["ip"], device["instanceId"]]

    device_data = cmdb_instance_search("FIREWALL@ONEMODEL")
    for device in device_data:
        if "dev_id" in device.keys():
            monitor_id[device["dev_id"]] = ["FIREWALL@ONEMODEL", device["ip"], device["instanceId"]]
            
    device_data = cmdb_instance_search("LOAD_BALANCE")
    for device in device_data:
        if "dev_id" in device.keys():
            monitor_id[device["dev_id"]] = ["LOAD_BALANCE", device["ip"], device["instanceId"]]

    device_data = None
    es_data = query_es()
    parser_es_data(es_data)
    
    for i in range(0,len(port_datas), 300):
        datas = port_datas[i:i+300]
        cmdb_import(datas)
#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json
import time

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'user': 'easyops',
    'org': '2024021401',
    'host': 'cmdb_resource.easyops-only.com',
    'content-type': 'application/json'
}

# 兼容 Python2处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

def cmdb_instance_search(object_id, params={}):
    """
    通用CMDB实例查询函数，根据传入的模型ID和查询参数返回实例列表
    """
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1
        else:
            print(ret)
            break
    return ret_list


def update_instance_permissions(object_id, instance_ids, users, fields, method=""):
    """
    批量修改CMDB实例权限
    参数:
    object_id - 模型ID
    instance_ids - 实例ID列表
    users - 用户/用户组列表
    fields - 权限字段列表，如 updateAuthorizers, deleteAuthorizers
    method - 权限修改方法，可选值: append(追加), overwrite(替换覆盖，结合put可以批量清空), pull(移除)
    """
    url = "http://***********:8079/permission/{}/instances/_batch".format(object_id)
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }

    print "实例权限设置结果：", json.dumps(payload, ensure_ascii=False, indent=2)

    response = requests.put(url, headers=headers, data=json.dumps(payload))
    return response


def main():
    # 记录整个脚本开始时间
    script_start_time = time.time()

    user_object_id = "USER"
    # lb_object_id = "LOAD_BALANCE" # 负载均衡
    # firewall_object_id = "FIREWALL@ONEMODEL" # 防火墙
    switch_object_id = "SWITCH@ONEMODEL"  # 交换机
    # route_object_id = "ROUTE@ONEMODEL"  # 路由器
    # relation_id = "_ASSETS_LOAD_BLANCE"
    # relation_id = "_ASSETS_FIREWALL"
    relation_id = "_ASSETS_SWITCH"
    # relation_id = "_ASSETS_ROUTE"
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    # method = "append"
    method = "overwrite" # 重设权限，覆盖老权限；结合put方法可以实现清空权限，list为为空即可
    # method = "pull"  # 移除权限



    assetResponsiblePerson_params = {
        "fields": {
            "instanceId": True,
            relation_id: True
        },
        "query": {
            "$and": [
                {relation_id: {"$exists": True}}
            ]
        }
    }

    # 查询负载均衡实例
    # query_start_time = time.time()
    # load_balance_instances = cmdb_instance_search(lb_object_id, assetResponsiblePerson_params)
    # query_end_time = time.time()
    # query_time = query_end_time - query_start_time

    # 查询防火墙实例
    # query_start_time = time.time()
    # firewall_instances = cmdb_instance_search(firewall_object_id, assetResponsiblePerson_params)
    # query_end_time = time.time()
    # query_time = query_end_time - query_start_time

    # 查询交换机实例
    query_start_time = time.time()
    switch_instances = cmdb_instance_search(switch_object_id, assetResponsiblePerson_params)
    query_end_time = time.time()
    query_time = query_end_time - query_start_time


    # 查询路由器实例
    # query_start_time = time.time()
    # router_instances = cmdb_instance_search(route_object_id, assetResponsiblePerson_params)
    # query_end_time = time.time()
    # query_time = query_end_time - query_start_time

    # print u"查询到的负载均衡实例总数:", len(load_balance_instances)
    # print u"查询到的防火墙实例总数:", len(firewall_instances)
    print u"查询到的交换机实例总数:", len(switch_instances)
    # print u"查询到的路由器实例总数:", len(router_instances)
    print "-----------遍历列表-----------"

    # 这里用字典缓存 nickname->name，避免重复查询
    user_cache = {}
    # 计数器
    update_instance_permissions_count = 0

    # 负载均衡
    # 记录处理负载均衡实例开始时间
    # process_start_time = time.time()
    # print u"开始处理负载均衡实例..."
    # instance_data_list = []
    # for lb in load_balance_instances:
    #     instance_id = lb.get("instanceId")
    #     _ASSETS_LOAD_BLANCE = lb.get(relation_id, [])
    #     print "负载均衡模型实例id：", instance_id
    #     for asset in _ASSETS_LOAD_BLANCE:
    #         arp = asset.get("assetResponsiblePerson")
    #         if arp and arp != "None":
    #             print "资产责任人：" , arp
    #             if arp in user_cache:
    #                 name = user_cache[arp]
    #             else:
    #                 ret = cmdb_instance_search(
    #                     user_object_id,
    #                     params={
    #                         "fields": {"name": True},
    #                         "query": {"nickname": {"$eq": arp}}
    #                     }
    #                 )
    #                 print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
    #                 name = None
    #                 if ret:
    #                     name = ret[0].get("name")
    #                 user_cache[arp] = name
    #                 print "用户名缓存：", name
    #                 print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)

    #             if instance_id and name:
    #                 instance_data_list.append({"instance_id": instance_id, "name": name})
    #                 update_instance_permissions_count += 1

    # # 批量处理负载均衡实例权限设置，每批次300个
    # for i in range(0, len(instance_data_list), 300):
    #     batch_data = instance_data_list[i:i+300]
    #     instance_ids = [item["instance_id"] for item in batch_data]
    #     users = list(set([item["name"] for item in batch_data]))  # 去重
    #     if instance_ids and users:
    #         response = update_instance_permissions(
    #             object_id=lb_object_id,
    #             instance_ids=instance_ids,
    #             users=users,
    #             fields=fields,
    #             method=method
    #         )
    #         print "状态码:", response.status_code
    #         print "响应内容:"
    #         try:
    #             print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    #         except:
    #             print(response.text)

    # 防火墙
    # 记录处理路由器实例开始时间
    # process_start_time = time.time()
    # print u"开始处理防火墙实例..."
    # instance_data_list = []
    # for firewall in firewall_instances:
    #     instance_id = firewall.get("instanceId")
    #     _ASSETS_FIREWALL = firewall.get(relation_id, [])
    #     print "防火墙模型实例id：", instance_id
    #     for asset in _ASSETS_FIREWALL:
    #         arp = asset.get("assetResponsiblePerson")
    #         if arp and arp != "None":
    #             print "资产责任人：" , arp
    #             if arp in user_cache:
    #                 name = user_cache[arp]
    #             else:
    #                 ret = cmdb_instance_search(
    #                     user_object_id,
    #                     params={
    #                         "fields": {"name": True},
    #                         "query": {"nickname": {"$eq": arp}}
    #                     }
    #                 )
    #                 print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
    #                 name = None
    #                 if ret:
    #                     name = ret[0].get("name")
    #                 user_cache[arp] = name
    #                 print "用户名缓存：", name
    #                 print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)

    #             if instance_id and name:
    #                 instance_data_list.append({"instance_id": instance_id, "name": name})
    #                 update_instance_permissions_count += 1

    # # 批量处理防火墙实例权限设置，每批次300个
    # for i in range(0, len(instance_data_list), 300):
    #     batch_data = instance_data_list[i:i+300]
    #     instance_ids = [item["instance_id"] for item in batch_data]
    #     users = list(set([item["name"] for item in batch_data]))  # 去重
    #     if instance_ids and users:
    #         response = update_instance_permissions(
    #             object_id=firewall_object_id,
    #             instance_ids=instance_ids,
    #             users=users,
    #             fields=fields,
    #             method=method
    #         )
    #         print "状态码:", response.status_code
    #         print "响应内容:"
    #         try:
    #             print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    #         except:
    #             print(response.text)

    # 交换机
    # 记录处理交换机实例开始时间
    process_start_time = time.time()
    print u"开始处理交换机实例..."
    instance_data_list = []
    for switch in switch_instances:
        instance_id = switch.get("instanceId")
        _ASSETS_SWITCH = switch.get(relation_id, [])
        print "交换机模型实例id：", instance_id
        for asset in _ASSETS_SWITCH:
            arp = asset.get("assetResponsiblePerson")
            if arp and arp != "None":
                print "资产责任人：" , arp
                if arp in user_cache:
                    name = user_cache[arp]
                else:
                    ret = cmdb_instance_search(
                        user_object_id,
                        params={
                            "fields": {"name": True},
                            "query": {"nickname": {"$eq": arp}}
                        }
                    )
                    print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
                    name = None
                    if ret:
                        name = ret[0].get("name")
                    user_cache[arp] = name
                    print "用户名缓存：", name
                    print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)

                if instance_id and name:
                    instance_data_list.append({"instance_id": instance_id, "name": name})
                    update_instance_permissions_count += 1

    # 批量处理交换机实例权限设置，每批次300个
    for i in range(0, len(instance_data_list), 300):
        batch_data = instance_data_list[i:i+300]
        instance_ids = [item["instance_id"] for item in batch_data]
        users = list(set([item["name"] for item in batch_data]))  # 去重
        if instance_ids and users:
            response = update_instance_permissions(
                object_id=switch_object_id,
                instance_ids=instance_ids,
                users=users,
                fields=fields,
                method=method
            )
            print "状态码:", response.status_code
            print "响应内容:"
            try:
                print(json.dumps(response.json(), indent=2, ensure_ascii=False))
            except:
                print(response.text)

    # 路由器
    # 记录处理路由器实例开始时间
    # process_start_time = time.time()
    # print u"开始处理路由器实例..."
    # instance_data_list = []
    # for route in router_instances:
    #     instance_id = route.get("instanceId")
    #     _ASSETS_ROUTE = route.get("_ASSETS_ROUTE", [])
    #     print "路由器模型实例id：", instance_id
    #     for asset in _ASSETS_ROUTE:
    #         arp = asset.get("assetResponsiblePerson")
    #         if arp and arp != "None":
    #             print "资产责任人：" , arp
    #             if arp in user_cache:
    #                 name = user_cache[arp]
    #             else:
    #                 ret = cmdb_instance_search(
    #                     user_object_id,
    #                     params={
    #                         "fields": {"name": True},
    #                         "query": {"nickname": {"$eq": arp}}
    #                     }
    #                 )
    #                 print "用户模型查询结果：", json.dumps(ret, ensure_ascii=False, indent=2)
    #                 name = None
    #                 if ret:
    #                     name = ret[0].get("name")
    #                 user_cache[arp] = name
    #                 print "用户名缓存：", name
    #                 print "用户缓存：", json.dumps(user_cache, ensure_ascii=False, indent=2)

    #             if instance_id and name:
    #                 instance_data_list.append({"instance_id": instance_id, "name": name})
    #                 update_instance_permissions_count += 1

    # # 批量处理路由器实例权限设置，每批次300个
    # for i in range(0, len(instance_data_list), 300):
    #     batch_data = instance_data_list[i:i+300]
    #     instance_ids = [item["instance_id"] for item in batch_data]
    #     users = list(set([item["name"] for item in batch_data]))  # 去重
    #     if instance_ids and users:
    #         response = update_instance_permissions(
    #             object_id=route_object_id,
    #             instance_ids=instance_ids,
    #             users=users,
    #             fields=fields,
    #             method=method
    #         )
    #         print "状态码:", response.status_code
    #         print "响应内容:"
    #         try:
    #             print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    #         except:
    #             print(response.text)

    # 记录处理实例结束时间
    process_end_time = time.time()
    process_time = process_end_time - process_start_time

    print "要设置的白名单总实例数：", update_instance_permissions_count

    # 计算并输出总耗时
    script_end_time = time.time()
    total_time = script_end_time - script_start_time

    # 只在脚本最后输出三个关键记录
    print u"\n========== 脚本执行时间统计 =========="
    print u"1. 查询实例耗时: %.2f 秒" % query_time
    print u"2. 设置实例白名单耗时: %.2f 秒" % process_time
    print u"3. 执行总耗时: %.2f 秒 (%.2f 分钟)" % (total_time, total_time/60)


if __name__ == "__main__":
    main()
import re
import io
import os
import sys
import json
import socket
import hashlib
import time
import datetime
import tarfile
import pexpect
import threading
import warnings
import paramiko as paramiko
import requests as requests
from concurrent.futures import ThreadPoolExecutor, as_completed



reload(sys)
sys.setdefaultencoding("utf8")
warnings.filterwarnings("ignore")
HOST = EASYOPS_CMDB_HOST.split(":")[0]


def cmdb_network_dervices_instance_search(device_type):
    headers = {
      'org': str(EASYOPS_ORG),
      'user': 'easyops',
      'Content-Type': 'application/json',
      "host":"cmdb_resource.easyops-only.com"
    }
    params={}
    url = "http://{}/object/{}/instance/_search".format(HOST, device_type)
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url,headers=headers,data=json.dumps(params)).text
        ret = json.loads(ret)
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1 
        else:
            print ret
            break
    return ret_list


def cmdb_import(backup_file, device_type, instanceId):
    url = "http://{}/object/{}/instance/{}?only_instance_id=true".format(HOST,device_type, instanceId)
    headers = {"user":"easyops","org":"2024021401","host":"cmdb_resource.easyops-only.com","content-type":"application/json"}
    payload = {
        "backup_file": backup_file
        }
    # print json.dumps(payload)
    response = requests.request("PUT", url, headers=headers, data=json.dumps(payload))
    print response.text


# 导入 CMDB 数据的函数
def cmdb_instance_import(objectId, datas):
    url = url = "http://{}/object/{}/instance/_import".format(HOST,objectId)  # 构造导入接口 URL
    headers = {
        'org': str(EASYOPS_ORG),  # 设置组织信息
        'user': 'easyops',        # 设置用户信息
        'Content-Type': 'application/json',
        "host": "cmdb_resource.easyops-only.com"  # 设置请求头
    }
    params = {
        "keys": ["instanceId"],  # 设置要使用的键
        "datas": datas    # 设置要导入的数据
    }
    # print json.dumps(params)
    # 发送 POST 请求将数据导入到 CMDB
    response = requests.request("POST", url, headers=headers, data=json.dumps(params))
    print(response.text)  # 打印返回结果

# 上传文件到cmdb附件 对应的路径
def upload_file(filepath):
    size = os.path.getsize(filepath)
    url = "http://{}:8138/api/v1/objectStore/bucket/cmdb-bucket/object".format(HOST)
    headers = {"user":"easyops","org":str(EASYOPS_ORG)}
    files = {"file":open(filepath,"rb")}
    response = requests.request("PUT",url,headers=headers,data={},files=files)
    print response.text
    if response.status_code == 200:
        objectName = response.json()["data"]["objectName"]
        objectUrl = "api/gateway/object_store.object_store.GetObject/api/v1/objectStore/bucket/cmdb-bucket/object/" + objectName
        name = os.path.basename(filepath)
        data = {"name":name,"url":objectUrl,"size":size}
        return data
    else:
        return {}

        
# 上传文件到制品库
def upload_artifact(packageId, name, memo, file_name):
    url = "http://{0}:8175/v2/archive".format(HOST)
    headers = {
      'org': str(EASYOPS_ORG),
      'user': 'easyops'
    }
    payload = {'packageId': packageId,
        'name': name,
        'memo': memo,
        'message': "",
        'env_type': "15",
        'unzip':'true',
        'stripFirst':'true'
        }
    # 以二进制方式打开文件
    with open(backup_dir + file_name, "rb") as f:
        files = {"file": (file_name, f, "application/zip")} 

        try:
            response = requests.request("POST", url, headers=headers, data=payload, files=files).text
            if json.loads(response)["code"] == 0:
                print("压缩包制品库上传完成...")
                return True
        except Exception as e:
            print("上传压缩包失败: {0}".format(e))
            return False
            

# 创建上传到制品库的tar.gz压缩包
def tar_all_folders(base_path, tar_file_name):
    """创建压缩包"""
    tar_file_path = os.path.join(base_path, tar_file_name)  # 目标 tar.gz 文件
    tar = tarfile.open(tar_file_path, "w:gz")

    for folder in os.listdir(base_path):
        folder_path = os.path.join(base_path, folder)
        if os.path.isdir(folder_path):  # 只处理文件夹
            tar.add(folder_path, arcname=os.path.basename(folder_path))  # 保留目录结构

    tar.close()
    print("压缩完成...")
    

def get_invoke_shell(ip, port, username, password):
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh_client.connect(ip, port, username, password)
    except paramiko.ssh_exception.AuthenticationException:
        print('登录失败！{0}'.format(ip))
        return None, None
    except paramiko.ssh_exception.NoValidConnectionsError:
        print('连接失败，请检查连接ip或端口是否有误！ {0}'.format(ip))
        return None, None
    except socket.error as e:
        print('连接失败：{0} {1}'.format(e, ip))
        return None, None
    except paramiko.ssh_exception.SSHException as e:
        print('ssh连接异常：{0} {1}'.format(e, ip))
        return None, None

    return ssh_client, ssh_client.invoke_shell()


def collect(ip, brand, device_type, cmd_list):
    """
    通过 SSH 连接设备，执行命令并收集输出
    
    :param ip: 设备 IP 地址
    :param device_type: 设备类型（交换机、路由器、防火墙等）
    :param cmd_list: 需要执行的命令列表
    """
    mac_cmd = None
    if device_type == "交换机":
        mac_cmd = [cmd_list[-1]]
        cmd_list = cmd_list[0:-1]
        # print(cmd_list)
        # print(mac_cmd)
    # 获取 SSH 连接，并开启交互式 Shell
    try:
        ssh_client, command = get_invoke_shell(ip, port=22, username=user, password=pwd)
        
        backup_output = exec_cmd(ssh_client,command, cmd_list, ip)
        mac_output = None
        if mac_cmd:
            # parse_mac_list(ip, brand, mac_data)
            mac_output = exec_cmd(ssh_client, command, mac_cmd, ip)
            all_device_mac_data[ip] = parse_mac_list(ip, brand, mac_output)
        if ssh_client:
            ssh_client.close()
    except Exception as e:
        print("error: {}".format(e))
    
    # 将输出结果写入文件
    write_file(ip, device_type, backup_output)


def now_date():
    """获取当前日期"""
    return datetime.datetime.now().strftime("%Y-%m-%d")


def write_file(ip,device_type, data):
    """写入文件数据，文件名格式: ************.txt"""
    filename = "{0}.txt".format(ip)
    filepath = os.path.join(backup_dir, device_type + "/", filename)
    # print(filepath)
    with open (filepath, "w") as f:
        f.write(data)
    
    print("{0} {1} backup is complete..".format(ip, device_type))


# 删除文件
def delete_files_in_subdirs(parent_dir):
    """
    删除 parent_dir 目录下的所有子文件夹中的 .txt 和 .tar.gz 文件
    """
    for root, dirs, files in os.walk(parent_dir):  # 遍历目录
        for file in files:
            if file.endswith(".txt") or file.endswith(".tar.gz") or file.endswith(".zip") or file.endswith(".json") or file.endswith(".ucs") or file.endswith(".bcf"):  # 过滤目标文件
                file_path = os.path.join(root, file)
                try:
                    # print(file_path)
                    # os.remove(file_path)  # 删除文件
                    print("Deleted:", file_path)
                except Exception as e:
                    print("Error deleting {0}: {1}".format(file_path, e))


# F5 负载均衡 ucs文件备份
def f5_backup(ip):
    ucs_name = "{0}.ucs".format(ip)
    cmd_save_ucs = "tmsh save sys ucs {0}".format(ucs_name)
    cmd_delete_ucs = "tmsh delete sys ucs {0}".format(ucs_name)
    # print(cmd_save_ucs)
    # print(cmd_delete_ucs)
    try:
        ssh_client, command = get_invoke_shell(ip, port=22, username=user, password=pwd)
        # 保存ucs文件
        command.send(cmd_save_ucs + '\n')  # 发送命令
        print("等待设备ucs文件备份完成..")
        time.sleep(10)
        # 使用scp传输ucs文件到备份服务器
        f5_scp_ucs(ip, ucs_name)
        # 删除ucs文件
        command.send(cmd_delete_ucs + "\n")
    except Exception as e:
        print("备份ucs文件失败: {}".format(e))
    finally:
        # 关闭 SSH 连接
        ssh_client.close()
        

# 使用scp传输ucs文件到备份服务器
def f5_scp_ucs(ip, ucs_name):
    scp_command = "scp {}@{}:{} {}".format(user, ip, os.path.join(ucs_file_path, ucs_name), ucs_backup_path)
    print(scp_command)
    child = pexpect.spawn(scp_command)
    child.expect("Password:")
    child.sendline(pwd)
    child.expect(pexpect.EOF)


# 深信服负载均衡获取 json 压缩包和bcf 文件token
def get_file_token(method, ip):
    headers = {
        'Authorization': 'Basic aXRtYW5hZ2VyOk1hbmFnZXJeNTU4NQ=='
    }
    url = "https://{}/api/lb/current-version/sys/backup-config-package".format(ip)
    
    if method == "GET":
        payload = {}
        
    elif method == "POST":
        payload = json.dumps({"type": "JSON-CONFIG-PACKAGE"})
    
    try:
        response = requests.request(method, url, headers=headers, data=payload, verify=False).text
        ret = json.loads(response)
        file_token = ret["file_token"]
        
        return file_token
    except Exception as e:
        print("获取文件token失败: {0}".format(e))
    print(file_token)
    

# 深信服负载均衡下载保存对应的文件
def export_file(ip):
    bcf_file_token = get_file_token("GET", ip)
    json_file_token = get_file_token("POST", ip)
    payload = {}
    headers = {
        'Authorization': 'Basic aXRtYW5hZ2VyOk1hbmFnZXJeNTU4NQ=='
    }
    # 下载json zip 压缩包并解压到对应的ip目录
    try:
        url = "https://{}/cgi/file-resource?d={}".format(ip, json_file_token)
        response = requests.request("GET", url, headers=headers, data=payload, verify=False)
        if response.status_code == 200:
            # 将压缩包读取到内存
            zip_file = zipfile.ZipFile(io.BytesIO(response.content))
             # 指定解压路径
            extract_path = os.path.join(backup_dir,"负载均衡", ip)
            if not os.path.exists(extract_path):
                os.makedirs(extract_path)
                # 解压压缩包
                zip_file.extractall(extract_path)
                print("{0} json 文件解压完成.".format(ip))
            else:
                 # 解压压缩包
                zip_file.extractall(extract_path)
                print("{0} json 文件解压完成.".format(ip))
    except Exception as e:
        print("{0} 获取json 压缩包失败，解压失败. {1}".format(ip, e))
    
    
    # 下载bcf文件
    try:
        url = "https://{}/cgi/file-resource?d={}".format(ip, bcf_file_token)
        response = requests.request("GET", url, headers=headers, data=payload, verify=False)
        # print(response.content)
        if response.status_code == 200:
            extract_path = os.path.join(backup_dir,"负载均衡/")
            with open("{0}{1}.bcf".format(extract_path, ip), "wb") as f:
                 # 以 1024 字节块写入
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            print("{0}{1}.bcf 保存成功".format(extract_path, ip))

    except Exception as e:
        print("{0} 获取bcf文件失败，解压失败: {1}".format(ip, e))



# 执行命令
def exec_cmd(ssh_client,command, cmd_list, ip):
    # 如果 SSH 连接失败，则直接返回
    if not ssh_client:
        return

    try:
        output = ""  # 存储设备返回的所有输出内容
        for cmd in cmd_list:
            command.send(cmd + '\n')  # 发送命令
            time.sleep(2)  # 等待设备返回数据
            while True:
                # print("{0} {1}执行中..".format(ip, cmd))
                time.sleep(1)  # 每次循环等待 1 秒
                chunk = command.recv(65535)  # 从设备接收数据
                output += chunk  # 累积数据到 output 变量
                
                # **检测设备是否启用了分页模式**
                if re.search(r'--More--|--\(less|---- More ----', chunk, re.IGNORECASE):
                    command.send(" ")  # 发送 `Space` 键翻页
                elif re.search(r'\(END\)|end|return|End', chunk, re.IGNORECASE):
                    break  # 如果检测到 `(END)`，说明是分页的最后一页，退出循环
                else:
                    break  # 正常情况下，收到完整输出后，退出循环
                
        return output
        
    except Exception as e:
        # 捕获异常，并打印错误信息
        print("{0} 网络设备查询错误: {1}".format(ip, e))


# 解析各厂家的mac list
def parse_mac_list(ip, brand, mac_data):
    result = []
   
    # Cisco 格式的正则
    cisco_pattern = re.compile(r'^\s*(All|\d+)\s+([\da-f]{4}\.[\da-f]{4}\.[\da-f]{4})\s+\w+\s+(\S+)', re.IGNORECASE)
    # 浪思 格式的正则（display mac-address 输出的风格）
    langsi_pattern = re.compile(r'(All|\d+)\s+([\da-f]{4}\.[\da-f]{4}\.[\da-f]{4})\s+\w+\s+(\S+)', re.IGNORECASE)
    # 华三，紫光恒越格式的正则
    huasan_pattern = re.compile(r'^\s*([\da-f]{4}-[\da-f]{4}-[\da-f]{4})\s+(\d+)\s+\w+\s+(\S+)\s+\w+', re.IGNORECASE)
    # 华为格式的正则
    huawei_pattren = re.compile(r'^\s*([\da-f]{4}-[\da-f]{4}-[\da-f]{4})\s+(\d+)/-\s+(\S+)\s+\w+\s+\d+', re.IGNORECASE)

    print("Parsing MAC address table for brand: {}\n".format(brand))

    if brand == '思科':
        lines = mac_data.strip().split('\n')
        for line in lines:
            match = cisco_pattern.match(line)
            if match:
                vlan, mac, port = match.groups()
                result.append({"vlan_id": vlan, "mac": mac, "port": port})
                
    elif brand == '浪思':
        # 去除 --More-- 和退格字符等杂项
        cleaned_data = re.sub(r'--More--|\x08', '', mac_data)
        lines = cleaned_data.strip().split('\n')
        for line in lines:
            match = langsi_pattern.search(line)
            if match:
                vlan, mac, port = match.groups()
                result.append({"vlan_id": vlan, "mac": mac, "port": port})
                
    elif brand == "华三" or brand == "紫光恒越":
        lines = mac_data.strip().split('\n')
        for line in lines:
            match = huasan_pattern.match(line)
            if match:
                mac, vlan, port = match.groups()
                result.append({"vlan_id": vlan, "mac": mac, "port": port})
    
    elif brand == "华为":
        lines = mac_data.strip().split('\n')
        for line in lines:
            match = huawei_pattren.match(line)
            if match:
                mac, vlan, port = match.groups()
                result.append({"vlan_id": vlan, "mac": mac, "port": port})
                
    return result
    # print(all_device_mac_data)    
  

# 运行网络设备命令
def process_single_device(i):
    print("[线程 %s] 正在处理IP: %s" % (threading.current_thread().name, i["ip"]))
    
    backup_cmd_list = json.loads(i["backup_cmd"][0])

    all_device_cmdb_data.append([i["ip"], i["type"], i["brand"]])

    # 非负载均衡的设备，收集数据
    if i["type"] != "负载均衡":
        collect(i["ip"], i["brand"], i["type"], backup_cmd_list)
            


# 获取cmdb各个网络设备实例数据
def get_all_device_result():
    switch_result = cmdb_network_dervices_instance_search("SWITCH@ONEMODEL")
    for i in switch_result:
        switch_ip_instanceId[i.get("ip")] = i["instanceId"]

    route_result = cmdb_network_dervices_instance_search("ROUTE@ONEMODEL")
    for i in route_result:
        route_ip_instanceId[i.get("ip")] = i["instanceId"]

    firewall_result = cmdb_network_dervices_instance_search("FIREWALL@ONEMODEL")
    for i in firewall_result:
        firewall_ip_instanceId[i.get("ip")] = i["instanceId"]

    loadblance_result = cmdb_network_dervices_instance_search("LOAD_BALANCE")
    for i in loadblance_result:
        loadblance_ip_instanceId[i.get("ip")] = i["instanceId"]
    


# 将配置文件上传到cmdb实例附件对应的目录
def packaged_cmdb_device_file():
    for device_data in all_device_cmdb_data:
        if device_data[0] in switch_ip_instanceId.keys():
            upload_file_path = os.path.join(backup_dir, "交换机/", "{}.txt".format(device_data[0]))
            upload_file_data = upload_file(upload_file_path)
            cmdb_import([upload_file_data], "SWITCH@ONEMODEL", switch_ip_instanceId[device_data[0]])
            cmdb_instance_import("SWITCH@ONEMODEL",[{"instanceId":switch_ip_instanceId[device_data[0]],"backup_date": today, "mac_list": all_device_mac_data.get(device_data[0])}])
            
        elif device_data[0] in route_ip_instanceId.keys():
            upload_file_path = os.path.join(backup_dir,"路由器/", "{}.txt".format(device_data[0]))
            upload_file_data = upload_file(upload_file_path)
            cmdb_import([upload_file_data], "ROUTE@ONEMODEL", route_ip_instanceId[device_data[0]])
            cmdb_instance_import("ROUTE@ONEMODEL",[{"instanceId":route_ip_instanceId[device_data[0]],"backup_date": today}])


        elif device_data[0] in firewall_ip_instanceId.keys():
            upload_file_path = os.path.join(backup_dir,"防火墙/", "{}.txt".format(device_data[0]))
            upload_file_data = upload_file(upload_file_path)
            cmdb_import([upload_file_data], "FIREWALL@ONEMODEL", firewall_ip_instanceId[device_data[0]])
            cmdb_instance_import("FIREWALL@ONEMODEL",[{"instanceId":firewall_ip_instanceId[device_data[0]],"backup_date": today}])
        
        elif device_data[0] in loadblance_ip_instanceId.keys():
            if device_data[2] == "深信服":
                upload_file_data = []
                json_file_path = os.path.join(backup_dir, "负载均衡", device_data[0])
                json_file_list = os.listdir(json_file_path)
                for i in json_file_list:
                    if i.endswith(".json"):
                        upload_file_data.append(upload_file(os.path.join(json_file_path,i)))
                cmdb_import(upload_file_data, "LOAD_BALANCE", loadblance_ip_instanceId[device_data[0]])
                
            elif device_data[2] == "F5":
                f5_backup(device_data[0])
                
            cmdb_instance_import("LOAD_BALANCE",[{"instanceId":loadblance_ip_instanceId[device_data[0]],"backup_date": today}])
                
        else:
            print("{} {} cmdb无此设备实例..".format(device_data[0], device_data[1]))



def main():
    lock = threading.Lock() 
    zip_file_name = "{0}.tar.gz".format(today)
    dervice_result = cmdb_network_dervices_instance_search("NETBACKUPPASSWORD@ONEMODEL")

     # 使用多线程
    with ThreadPoolExecutor(max_workers=10) as executor:  # 可以根据机器性能调 max_workers
        executor.map(process_single_device, dervice_result)
    
    get_all_device_result()
    packaged_cmdb_device_file()

    # 将所有配置文件打包成tar.gz
    tar_all_folders(backup_dir, zip_file_name)
    # 上传压缩包到制品管理
    upload_zip_status = upload_artifact(packageId, today, "backup", zip_file_name)
    
    # 删除配置文件和压缩包
    if upload_zip_status:
        delete_files_in_subdirs(backup_dir)
    else:
        print("上传压缩包失败,不执行删除配置文件操作。")


if __name__ == "__main__":
    all_device_mac_data = {}
    all_device_cmdb_data = []
    switch_ip_instanceId = {}
    route_ip_instanceId = {}
    firewall_ip_instanceId = {}
    loadblance_ip_instanceId = {}
    switch_data = []
    route_data = []
    firewall_data = []
    loadblance_data = []
    
    user = "itmanager"
    pwd = "Manager^5585"
    ucs_file_path = "/var/local/ucs/"
    ucs_backup_path = "/data/net_device_backups/负载均衡/"
    packageId = "8df1f1ccba7d4d47833f74b7c7c7fa13"
    backup_dir = "/data/net_device_backups/"
    headers = {
      'org': str(EASYOPS_ORG),
      'user': 'easyops',
      'Content-Type': 'application/json',
      'host': 'deployrepo.easyops-only.com'
    }
    today = now_date()
    main()

#--*--coding:utf8--*-- 

# 导入必要的库
import requests
import json

# HOST = EASYOPS_CMDB_HOST.split(':')[0]
HOST = '***********'
EASYOPS_ORG = '2024021401'
print(HOST)
print(EASYOPS_ORG)
headers = {
        'org': str(EASYOPS_ORG),  # 设置组织信息
         'user': 'easyops',        # 设置用户信息
        'Content-Type': 'application/json',
        "host": "cmdb_resource.easyops-only.com"  # 设置请求头
}

# 搜索 CMDB 中实例数据的函数
def cmdb_instance_search(device_type, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, device_type)  # 构造查询接口 URL
    ret_list = []  # 存储查询结果
    page = 1  # 初始页码
    page_size = 200  # 每页大小
    
    while True:
        params["page"] = page  # 设置分页参数
        params["page_size"] = page_size
        
        # 发送请求获取数据
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        
        if ret["code"] == 0:  # 如果查询成功
            ret_list += ret["data"]["list"]  # 将数据添加到结果列表
            
            if len(ret["data"]["list"]) < page_size:  # 如果当前页数据不足 page_size，说明已是最后一页
                break
            page += 1  # 否则继续请求下一页
        else:
            print(ret)  # 打印错误信息
            break
    
    return ret_list  # 返回所有查询到的数据
    
# 导入 CMDB 数据的函数
def import_cmdb(objectId, datas):
    url = "http://{}/object/{}/instance/_import".format(HOST,objectId)  # 构造导入接口 URL
    params = {
        "keys": ["assetNumber"],  # 设置要使用的键
        "datas": datas    # 设置要导入的数据
    }
    
    # 发送 POST 请求将数据导入到 CMDB
    response = requests.request("POST", url, headers=headers, data=json.dumps(params))
    print(response.text)  # 打印返回结果

if __name__ == '__main__':
    data_list = []
    router_result = {}
    # params = {"query":{"ip":{"$exists":True}}}
    assets_data = cmdb_instance_search("ASSETS",params={"query":{"assetNumber":{"$exists":True}}})
    router_data = cmdb_instance_search("ROUTE@ONEMODEL",params={"query":{"propertyid":{"$exists":True}}})

    # 关联路由器
    # print "查询到的路由器实例总数为：",len(router_data) # python2中，print为语句
    print("查询到的路由器实例总数为：",len(router_data))  # python3中，print为函数
    for i in router_data:
        # print i
        # 字典中新增propertyid 和 instanceId的键值对;类似这种"10082711": "6311369828762"
        router_result[i["propertyid"]] = i["instanceId"]
    # print json.dumps(router_result, ensure_ascii=False, indent=2)    
    print(json.dumps(router_result, ensure_ascii=False, indent=2))    
    
    # print "查询到的资产实例总数为：",len(assets_data)
    print("查询到的资产实例总数为：",len(assets_data))
    for i in assets_data:
        # 定义空字典
        data = {}
        # 将从资产模型获取到的资产编号赋值给assetNumber变量
        assetNumber = i.get("assetNumber")
        # print assetNumber
        # 从router_result字典中获取与assetNumber变量的值相同的键名对应的值，也就是获取到的路由器实例ID
        # 例如：如果assetNumber的值为"10082711"，则从router_result字典中获取到的值为"6311369828762"
        instanceId = router_result.get(assetNumber)
        # print json.dumps(instanceId, ensure_ascii=False, indent=2) 
        # 如果instanceId变量对应的值存在，比如值为"6311369828762"
        # 也就是说从router_result字典中获取到的值不为空
        if instanceId:
            # 往data空字典中添加键名为"_ROUTE_ASSETS"，value为变量instanceId的字典
            data["_ROUTE_ASSETS"] = instanceId
            # 往data空字典中添加键名为"assetNumber"，value为变量assetNumber的字典
            data["assetNumber"] = assetNumber
            # 把data字典添加到data_list列表
            data_list.append(data)
            

    # print "导入cmdb的实例总数为：",len(data_list)
    print("导入cmdb的实例总数为：",len(data_list))
    # print json.dumps(data_list, ensure_ascii=False, indent=2)
    print(json.dumps(data_list, ensure_ascii=False, indent=2))
    # for i in range(0,len(data_list),500):
    #     datas = data_list[i:i+500]
    #     import_cmdb("ASSETS", datas)
    #     print "导入数据成功,当前数据量为：",len(datas)
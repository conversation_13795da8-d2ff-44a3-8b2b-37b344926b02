#!/usr/local/easyops/python/bin/python
# coding=utf8
import dmPython
import requests
import json


HOST = EASYOPS_CMDB_HOST.split(":")[0]

def connect_dm():
    # 创建连接数据库对象
    conn = dmPython.connect(user='cmdb_read', password='cmdbread20250106', server='***********',port=5236,local_code=1)
    # 创建游标
    cursor = conn.cursor()
    data_list = []
    # 执行查询
    try:
        cursor.execute("""
        SELECT tf.field0001 AS "资产编号", 
               tf.field0069 AS "资产名称-手填", 
               tf.field0004 AS "资产规格", 
               tctp.showvalue AS "资产分类", 
               tu.name AS "所属部门", 
               tf.field0006 AS "财务存放位置", 
               tf.field0008 AS "资产原值", 
               tm.name AS "资产领物人", 
               tf.field0071 AS "资产责任人", 
               '' AS "责任人", 
               tf.field0010 AS "数量", 
               tf.field0011 AS "购买日期", 
               tf.field0079 AS "实物存放位置", 
               tf.field0020 AS "含税金额", 
               tf.field0109 AS "供货商", 
               decode(tf.field0013, 1589761072551841390, '正常使用', 5328068829990043758, '已报废', '未知') AS "资产状态" 
        FROM V3XUSER.formmain_12821 tf
        JOIN V3XUSER.ORG_UNIT tu ON tf.field0007 = tu.id
        LEFT JOIN V3XUSER.ORG_MEMBER tm ON tf.field0009 = tm.id
        JOIN V3XUSER.CTP_ENUM_ITEM tctp ON tf.field0005 = tctp.id
        WHERE tu.id IN ('-5215309333052895253', '-8989856161901032203') and rownum < 3
        """)
    
        # 获取结果
        rows = cursor.fetchall()
        print(rows)
        for row in rows:
            info = {}
            data = [str(i) for i in row]
            info["assetNumber"] = data[0]
            info["name"] = data[1]
            info["assetSpecifications"] = data[2]
            info["assetClassification"] = data[3]
            info["department"] = data[4]
            info["financialStorageLocation"] = data[5]
            info["originalAssetValue"] = data[6]
            info["assetRecipient"] = data[7]
            info["assetResponsiblePerson"] = data[8]
            info["personLiable"] = data[9]
            info["quantity"] = data[10]
            info["purchaseDate"] = data[11]
            info["physicalStorageLocation"] = data[12]
            info["taxInclusiveAmount"] = data[13]
            info["supplier"] = data[14]
            info["assetStatus"] = data[15]
            user_name = info["assetResponsiblePerson"]
            
            user_instanceId = user_name_instanceId_dict.get(user_name.replace(" ","").decode("utf8"), None)
            
            info["USER"] = [{"instanceId": user_instanceId}]
            
            data_list.append(info)
        print len(data_list)
        return data_list

    except Exception as e:
        print("查询失败：", e)

    # 关闭游标和连接
    finally:
        cursor.close()
        conn.close()
        print("连接已关闭")
        
    
def cmdb_user_instance_search(params={}):
    url = "http://{}/object/TYRZ_USER/instance/_search".format(HOST)
    headers = {
      'org': str(EASYOPS_ORG),
      'user': 'easyops',
      'Content-Type': 'application/json',
      "host":"cmdb_resource.easyops-only.com"
    }
    ret_list = []
    page = 1
    page_size = 200
    while True:
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url,headers=headers,data=json.dumps(params)).json()
        if ret["code"] == 0:
            ret_list += ret["data"]["list"]
            if len(ret["data"]["list"]) < page_size:
                break
            page += 1 
        else:
            print ret
            break
    return ret_list


def cmdb_import(datas):
    url = "http://{}/object/ASSETS/instance/_import".format(HOST)
    headers = {"user":"easyops","org":"2024021401","host":"cmdb_resource.easyops-only.com","content-type":"application/json"}
    payload = {
        "keys":["assetNumber"],
        "datas":datas
        }
    print json.dumps(payload)
    # response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    # print response.text
    

if __name__ == "__main__":
    user_name_instanceId_dict = {}
    users = cmdb_user_instance_search()
    for user in users:
        user_name_instanceId_dict[user["fullname"]] = user["instanceId"]
    
    data_list = connect_dm()
    # print len(data_list)
    for i in range(0,len(data_list),500):
        datas = data_list[i:i+500]
        cmdb_import(datas)
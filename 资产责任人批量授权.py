#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import sys
import requests
import json

# 兼容 Python 2 处理默认编码
reload(sys)
sys.setdefaultencoding("utf-8")

HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'org': str(EASYOPS_ORG),
    'user': 'easyops',
    'Content-Type': 'application/json',
    "host": "cmdb_resource.easyops-only.com"
}

def cmdb_instance_search(object_id, params={}):
    """
    搜索CMDB实例数据
    
    参数:
    object_id - 模型ID
    params - 查询参数
    
    返回:
    符合条件的实例列表
    """
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []  # 初始化返回列表
    page = 1  # 页码，初始为第一页
    page_size = 200  # 每页返回的数据条数
    
    while True:
        # 设置分页参数
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        
        if ret["code"] == 0:  # 如果响应正常
            ret_list += ret["data"]["list"]  # 将当前页的数据添加到返回列表
            if len(ret["data"]["list"]) < page_size: # 如果返回的数据小于每页条数，说明已到达最后一页
                break
            # 否则，继续请求下一页
            page += 1
        else:
            print(ret) # 如果响应错误，打印错误信息并退出循环
            break
    return ret_list  # 返回所有符合条件的实例数据

def update_instance_permissions(object_id, instance_ids, users, fields, method="append"):
    """
    批量修改CMDB实例权限
    
    参数:
    object_id - 模型ID
    instance_ids - 实例ID列表
    users - 用户/用户组列表
    fields - 权限字段列表，如 updateAuthorizers, deleteAuthorizers
    method - 权限修改方法，可选值: append(追加), overwrite(替换), pull(移除)
    
    返回:
    响应结果的JSON
    """
    # 构建API URL
    url = "http://{}/permission/{}/instances/_batch".format(HOST, object_id)
    
    # 构建请求体
    payload = {
        "method": method,
        "list": users,
        "fields": fields,
        "ids": instance_ids
    }
    
    # 发送PUT请求
    response = requests.put(url, headers=headers, data=json.dumps(payload))
    
    # 返回响应结果
    return response

def main():
    # 模型ID
    object_id = "ASSETS"  # 资产模型ID
    
    # 查询条件：assetResponsiblePerson不为空
    params = {
        "query": {
            "assetResponsiblePerson": {
                "$exists": True,
                "$ne": ""
            }
        }
    }
    
    # 查询符合条件的资产实例
    assets = cmdb_instance_search(object_id, params)
    print u"查询到的资产实例总数:", len(assets)
    
    # 按资产责任人分组
    responsible_persons = {}
    for asset in assets:
        if "assetResponsiblePerson" in asset and "instanceId" in asset:
            person = asset["assetResponsiblePerson"]
            if person not in responsible_persons:
                responsible_persons[person] = []
            responsible_persons[person].append(asset["instanceId"])
    
    # 权限字段列表
    fields = ["updateAuthorizers", "deleteAuthorizers"]
    
    # 权限修改方法
    method = "append"  # 可选: append, overwrite, pull
    
    # 为每个责任人批量修改其负责资产的权限
    for person, instance_ids in responsible_persons.items():
        print u"正在处理责任人 {} 的 {} 个资产实例".format(person, len(instance_ids))
        
        # 调用批量修改权限函数
        response = update_instance_permissions(
            object_id=object_id,
            instance_ids=instance_ids,
            users=[person],  # 将责任人作为授权用户
            fields=fields,
            method=method
        )
        
        # 打印响应结果
        print u"状态码:", response.status_code
        try:
            # 尝试格式化输出JSON响应
            print json.dumps(response.json(), indent=2, ensure_ascii=False)
        except:
            # 如果不是有效的JSON，直接打印文本
            print response.text
        print "-" * 50

if __name__ == "__main__":
    main()
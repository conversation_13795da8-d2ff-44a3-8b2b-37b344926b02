#!/usr/local/easyops/python/bin/python
# -*- coding: utf-8 -*-

import requests
import json
HOST = EASYOPS_CMDB_HOST.split(":")[0]

headers = {
    'org': str(EASYOPS_ORG),
    'user': 'easyops',
    'Content-Type': 'application/json',
    "host": "cmdb_resource.easyops-only.com"
}

def cmdb_instance_search(object_id, params={}):
    url = "http://{}/object/{}/instance/_search".format(HOST, object_id)
    ret_list = []  # 初始化返回列表，用于存储符合条件的数据
    page = 1  # 页码，初始为第一页
    page_size = 200  # 每页返回的数据条数
    while True:
        # 设置分页参数
        params["page"] = page
        params["page_size"] = page_size
        ret = requests.post(url, headers=headers, data=json.dumps(params)).json()
        if ret["code"] == 0:  # 如果响应正常
            ret_list += ret["data"]["list"]  # 将当前页的数据添加到返回列表
            if len(ret["data"]["list"]) < page_size: # 如果返回的数据小于每页条数，说明已到达最后一页
                break
            # 否则，继续请求下一页
            page += 1
        else:
            print(ret) # 如果响应错误，打印错误信息并退出循环
            break
    return ret_list  # 返回所有符合条件的实例数据

def cmdb_import(objectId, datas):
    url = "http://{}/object/{}/instance/_import".format(HOST, objectId)
    payload = {
        "keys": ["propertyid"],
        "datas": datas
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    print json.dumps(json.loads(response.text), indent=4, ensure_ascii=False)

def generate_result():
    route_params={"query":{"propertyid":{"$exists":True}}}
    route_instances = cmdb_instance_search("ROUTE@ONEMODEL", route_params)
    
    propertyid_list = []
    for obj in route_instances:
        if 'propertyid' in obj:
            propertyid_list.append(obj['propertyid'])
    
    final_data = []
    
    for propertyid in propertyid_list:
        print json.dumps(propertyid, ensure_ascii=False, indent=2) 
        asset_params = {"fields": {"instanceId": True}, "query": {"assetNumber": {"$eq": propertyid}}}
        ret = cmdb_instance_search("ASSETS", asset_params)
        # if ret and len(ret) > 0:
        # 判断是否有元素存在
        if ret:
        # if len(ret) > 0:    # 或者使用 if ret: 来判断是否有数据
            instanceId = ret[0]["instanceId"]
            #由于查询条件中使用了fields: {"instanceId": True},且查询结果存在，因此instanceId必定存在
            data = {
                "propertyid": propertyid,
                "_ASSETS_ROUTE": [{"instanceId": instanceId}]
            }
            final_data.append(data)
        else:
            print "警告: 未找到propertyid为 {} 的资产".format(propertyid) 

    return final_data

if __name__ == "__main__":
    datas = generate_result()
    print len(datas)
    print(json.dumps(datas, ensure_ascii=False, indent=2))
    cmdb_import("ROUTE@ONEMODEL", datas)